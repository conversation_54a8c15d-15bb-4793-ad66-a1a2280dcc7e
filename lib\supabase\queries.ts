import { supabase } from './client'
import { UserProfile, Category, Transaction, RecurringPayment, Goal, Loan } from '@/lib/types/database'

// User Profile Operations
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) throw error
  return data
}

export const updateUserProfile = async (userId: string, updates: Partial<UserProfile>) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Category Operations
export const getUserCategories = async (userId: string) => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('user_id', userId)
    .order('name')
  
  if (error) throw error
  return data
}

export const createCategory = async (category: Omit<Category, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('categories')
    .insert(category)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Transaction Operations
export const getUserTransactions = async (userId: string, limit?: number) => {
  let query = supabase
    .from('transactions')
    .select(`
      *,
      category:categories(*)
    `)
    .eq('user_id', userId)
    .order('transaction_date', { ascending: false })
  
  if (limit) {
    query = query.limit(limit)
  }
  
  const { data, error } = await query
  
  if (error) throw error
  return data
}

export const createTransaction = async (transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('transactions')
    .insert(transaction)
    .select(`
      *,
      category:categories(*)
    `)
    .single()
  
  if (error) throw error
  return data
}

export const updateTransaction = async (id: string, updates: Partial<Transaction>) => {
  const { data, error } = await supabase
    .from('transactions')
    .update(updates)
    .eq('id', id)
    .select(`
      *,
      category:categories(*)
    `)
    .single()
  
  if (error) throw error
  return data
}

export const deleteTransaction = async (id: string) => {
  const { error } = await supabase
    .from('transactions')
    .delete()
    .eq('id', id)
  
  if (error) throw error
}

// Recurring Payment Operations
export const getUserRecurringPayments = async (userId: string) => {
  const { data, error } = await supabase
    .from('recurring_payments')
    .select(`
      *,
      category:categories(*)
    `)
    .eq('user_id', userId)
    .order('next_payment_date')
  
  if (error) throw error
  return data
}

export const createRecurringPayment = async (payment: Omit<RecurringPayment, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('recurring_payments')
    .insert(payment)
    .select(`
      *,
      category:categories(*)
    `)
    .single()
  
  if (error) throw error
  return data
}

// Goal Operations
export const getUserGoals = async (userId: string) => {
  const { data, error } = await supabase
    .from('goals')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export const createGoal = async (goal: Omit<Goal, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('goals')
    .insert(goal)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export const updateGoal = async (id: string, updates: Partial<Goal>) => {
  const { data, error } = await supabase
    .from('goals')
    .update(updates)
    .eq('id', id)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Loan Operations
export const getUserLoans = async (userId: string) => {
  const { data, error } = await supabase
    .from('loans')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export const createLoan = async (loan: Omit<Loan, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('loans')
    .insert(loan)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Financial Summary Operations
export const getMonthlyFinancialSummary = async (userId: string, year: number, month: number) => {
  const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
  const endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`
  
  const { data, error } = await supabase
    .from('transactions')
    .select('type, amount, currency_code')
    .eq('user_id', userId)
    .gte('transaction_date', startDate)
    .lt('transaction_date', endDate)
  
  if (error) throw error
  
  const summary = data.reduce((acc, transaction) => {
    if (transaction.type === 'income') {
      acc.income += transaction.amount
    } else {
      acc.expenses += transaction.amount
    }
    return acc
  }, { income: 0, expenses: 0 })
  
  return {
    ...summary,
    balance: summary.income - summary.expenses
  }
}
