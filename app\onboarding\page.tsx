'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useAuth } from '@/lib/contexts/auth-context'
import { AuthGuard } from '@/lib/middleware/auth-guard'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { CheckCircle } from 'lucide-react'

// Import onboarding steps
import { ProfileSetup } from '@/components/onboarding/profile-setup'
import { CurrencySelection } from '@/components/onboarding/currency-selection'
import { CategoryManagement } from '@/components/onboarding/category-management'
import { OnboardingComplete } from '@/components/onboarding/onboarding-complete'

const ONBOARDING_STEPS = [
  { id: 1, title: 'Profile Setup', description: 'Set up your personal information' },
  { id: 2, title: 'Currency & Income', description: 'Configure your financial preferences' },
  { id: 3, title: 'Categories', description: 'Organize your spending categories' },
  { id: 4, title: 'Complete', description: 'You\'re all set!' },
]

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const { user, profile, refreshProfile } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Check if user has already completed onboarding
    if (profile && profile.full_name && profile.currency_code && profile.monthly_income) {
      // User has completed basic setup, redirect to dashboard
      router.push('/dashboard')
    }
  }, [profile, router])

  const handleStepComplete = async (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps(prev => [...prev, stepId])
    }
    
    // Refresh profile data
    await refreshProfile()
    
    // Move to next step
    if (stepId < ONBOARDING_STEPS.length) {
      setCurrentStep(stepId + 1)
    }
  }

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSkipToStep = (stepId: number) => {
    setCurrentStep(stepId)
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <ProfileSetup
            onComplete={() => handleStepComplete(1)}
            onSkip={() => handleStepComplete(1)}
          />
        )
      case 2:
        return (
          <CurrencySelection
            onComplete={() => handleStepComplete(2)}
            onBack={handlePreviousStep}
          />
        )
      case 3:
        return (
          <CategoryManagement
            onComplete={() => handleStepComplete(3)}
            onBack={handlePreviousStep}
          />
        )
      case 4:
        return (
          <OnboardingComplete
            onComplete={() => router.push('/dashboard')}
          />
        )
      default:
        return null
    }
  }

  const progressPercentage = (currentStep / ONBOARDING_STEPS.length) * 100

  return (
    <AuthGuard requireAuth={true}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome to Personal Finance Tracker
            </h1>
            <p className="text-gray-600">
              Let's set up your account to get the most out of your financial tracking
            </p>
          </div>

          {/* Progress Bar */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium text-gray-700">
                  Step {currentStep} of {ONBOARDING_STEPS.length}
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round(progressPercentage)}% Complete
                </span>
              </div>
              <Progress value={progressPercentage} className="mb-4" />
              
              {/* Step indicators */}
              <div className="flex justify-between">
                {ONBOARDING_STEPS.map((step) => (
                  <div
                    key={step.id}
                    className={`flex flex-col items-center cursor-pointer ${
                      step.id <= currentStep ? 'text-blue-600' : 'text-gray-400'
                    }`}
                    onClick={() => step.id < currentStep && handleSkipToStep(step.id)}
                  >
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${
                        completedSteps.includes(step.id)
                          ? 'bg-green-500 text-white'
                          : step.id === currentStep
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-400'
                      }`}
                    >
                      {completedSteps.includes(step.id) ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        step.id
                      )}
                    </div>
                    <div className="text-center">
                      <div className="text-xs font-medium">{step.title}</div>
                      <div className="text-xs text-gray-500 hidden sm:block">
                        {step.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Current Step Content */}
          <div className="mb-8">
            {renderCurrentStep()}
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
