'use client'

import { AppLayout } from '@/components/layouts/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { CreditCard, Plus, Building, User, Calendar } from 'lucide-react'

export default function LoansDebtsPage() {
  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Loans & Debts</h1>
            <p className="text-gray-600">
              Track and manage your loans, debts, and payment schedules
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Loan/Debt
          </Button>
        </div>

        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Debt</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">$0.00</div>
              <p className="text-xs text-gray-500">Outstanding balance</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Monthly Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">$0.00</div>
              <p className="text-xs text-gray-500">Total monthly</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Loans</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">0</div>
              <p className="text-xs text-gray-500">Currently tracking</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Next Payment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">--</div>
              <p className="text-xs text-gray-500">Days until due</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="bank-loans" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="bank-loans">Bank Loans</TabsTrigger>
            <TabsTrigger value="personal-debts">Personal Debts</TabsTrigger>
            <TabsTrigger value="payment-calendar">Payment Calendar</TabsTrigger>
          </TabsList>

          <TabsContent value="bank-loans">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Building className="h-5 w-5" />
                  <CardTitle>Bank Loans</CardTitle>
                </div>
                <CardDescription>
                  Mortgages, auto loans, and other institutional loans
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    No bank loans tracked
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Add your mortgages, auto loans, and other bank loans
                  </p>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Bank Loan
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="personal-debts">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <CardTitle>Personal Debts</CardTitle>
                </div>
                <CardDescription>
                  Money owed to friends, family, or personal creditors
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    No personal debts tracked
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Keep track of money owed to friends and family
                  </p>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Personal Debt
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payment-calendar">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <CardTitle>Payment Calendar</CardTitle>
                </div>
                <CardDescription>
                  View all upcoming loan and debt payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    No payments scheduled
                  </h3>
                  <p className="text-gray-600">
                    Add loans and debts to see your payment schedule
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Debt Management Tips</CardTitle>
            <CardDescription>
              Strategies to help you manage and reduce your debt
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Debt Snowball Method</h4>
                <p className="text-sm text-gray-600">
                  Pay minimum on all debts, then put extra money toward the smallest debt first.
                </p>
              </div>
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Debt Avalanche Method</h4>
                <p className="text-sm text-gray-600">
                  Pay minimum on all debts, then put extra money toward the highest interest rate debt.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
