'use client'

import { AppLayout } from '@/components/layouts/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Help, Search, Book, MessageCircle, Mail, FileText } from 'lucide-react'

export default function HelpPage() {
  return (
    <AppLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Help & Support</h1>
          <p className="text-gray-600">
            Find answers to your questions and get help with Personal Finance Tracker
          </p>
        </div>

        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search for help articles, features, or common questions..."
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Book className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle>Getting Started Guide</CardTitle>
                  <CardDescription>
                    Learn the basics of using Personal Finance Tracker
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Step-by-step tutorials for setting up your account and tracking finances
              </p>
              <Button variant="outline" className="w-full">
                View Guide
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MessageCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <CardTitle>AI Chat Help</CardTitle>
                  <CardDescription>
                    Learn how to use the AI assistant effectively
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Tips for uploading receipts and getting financial advice from AI
              </p>
              <Button variant="outline" className="w-full">
                AI Features
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <FileText className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <CardTitle>FAQ</CardTitle>
                  <CardDescription>
                    Frequently asked questions and answers
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Common questions about features, billing, and troubleshooting
              </p>
              <Button variant="outline" className="w-full">
                Browse FAQ
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Mail className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <CardTitle>Contact Support</CardTitle>
                  <CardDescription>
                    Get help from our support team
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Send us a message and we'll get back to you within 24 hours
              </p>
              <Button variant="outline" className="w-full">
                Contact Us
              </Button>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Popular Help Topics</CardTitle>
            <CardDescription>
              Quick answers to common questions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  question: "How do I add my first transaction?",
                  answer: "Go to the Transactions page and click 'Add Transaction'. Fill in the amount, category, and description."
                },
                {
                  question: "Can I upload receipts to automatically create transactions?",
                  answer: "Yes! Use the AI Chat feature to upload receipt images and the AI will extract transaction details automatically."
                },
                {
                  question: "How do I set up recurring payments?",
                  answer: "Visit the Recurring Payments page and click 'Add Recurring Payment'. Set the frequency and amount for automatic tracking."
                },
                {
                  question: "How do I change my currency?",
                  answer: "Go to Settings > Profile Settings to update your base currency and other preferences."
                },
                {
                  question: "Can I export my financial data?",
                  answer: "Yes, go to Settings > Data Export to download your transactions and other data in various formats."
                }
              ].map((item, index) => (
                <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <h4 className="font-medium text-gray-900 mb-2">{item.question}</h4>
                  <p className="text-sm text-gray-600">{item.answer}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
