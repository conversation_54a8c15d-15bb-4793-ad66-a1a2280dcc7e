export type TransactionType = 'income' | 'expense';
export type RecurringFrequency = 'daily' | 'weekly' | 'monthly' | 'yearly';
export type GoalStatus = 'active' | 'completed' | 'paused';
export type LoanType = 'bank_loan' | 'personal_debt';

export interface UserProfile {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  currency_code: string;
  monthly_income: number | null;
  salary_payment_date: number | null;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  user_id: string;
  name: string;
  color: string | null;
  icon: string | null;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  category_id: string | null;
  type: TransactionType;
  amount: number;
  currency_code: string;
  description: string | null;
  merchant_name: string | null;
  transaction_date: string;
  receipt_url: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  category?: Category;
}

export interface RecurringPayment {
  id: string;
  user_id: string;
  category_id: string | null;
  name: string;
  amount: number;
  currency_code: string;
  frequency: RecurringFrequency;
  start_date: string;
  end_date: string | null;
  next_payment_date: string;
  is_active: boolean;
  description: string | null;
  created_at: string;
  updated_at: string;
  category?: Category;
}

export interface Goal {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  target_amount: number;
  current_amount: number;
  currency_code: string;
  target_date: string | null;
  status: GoalStatus;
  created_at: string;
  updated_at: string;
}

export interface Loan {
  id: string;
  user_id: string;
  type: LoanType;
  name: string;
  principal_amount: number;
  current_balance: number;
  interest_rate: number | null;
  currency_code: string;
  start_date: string;
  end_date: string | null;
  monthly_payment: number | null;
  lender_name: string | null;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface AIConversation {
  id: string;
  user_id: string;
  title: string | null;
  messages: any[];
  created_at: string;
  updated_at: string;
}

export interface UserSettings {
  id: string;
  user_id: string;
  dashboard_widgets: Record<string, any>;
  ai_preferences: Record<string, any>;
  notification_settings: Record<string, any>;
  theme_preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Database schema type for Supabase
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: UserProfile;
        Insert: Omit<UserProfile, 'created_at' | 'updated_at'>;
        Update: Partial<Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>>;
      };
      categories: {
        Row: Category;
        Insert: Omit<Category, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Category, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      transactions: {
        Row: Transaction;
        Insert: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Transaction, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      recurring_payments: {
        Row: RecurringPayment;
        Insert: Omit<RecurringPayment, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<RecurringPayment, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      goals: {
        Row: Goal;
        Insert: Omit<Goal, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Goal, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      loans: {
        Row: Loan;
        Insert: Omit<Loan, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Loan, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      ai_conversations: {
        Row: AIConversation;
        Insert: Omit<AIConversation, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<AIConversation, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      user_settings: {
        Row: UserSettings;
        Insert: Omit<UserSettings, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<UserSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
    };
  };
}
