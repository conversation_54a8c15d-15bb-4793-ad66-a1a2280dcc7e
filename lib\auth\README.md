# Authentication System

This directory contains the authentication system for the Personal Finance Tracker application using Supabase Auth.

## Features Implemented

### 1. Authentication Context (`lib/contexts/auth-context.tsx`)
- React context for managing authentication state
- User session management
- Profile data loading and caching
- Sign up, sign in, and sign out functions
- Profile update functionality

### 2. Route Protection (`lib/middleware/auth-guard.tsx`)
- Client-side route protection component
- Loading states during authentication checks
- Automatic redirects based on authentication status
- Flexible configuration for protected/public routes

### 3. Server-side Middleware (`middleware.ts`)
- Next.js middleware for server-side route protection
- Automatic redirects for authenticated/unauthenticated users
- Session management using Supabase SSR
- Protected routes: `/dashboard`, `/onboarding`, `/settings`
- Auth routes: `/auth/*`

### 4. Authentication Pages

#### Sign Up (`app/auth/signup/page.tsx`)
- User registration form with validation
- Full name, email, and password fields
- Password confirmation
- Form validation and error handling
- Success states and redirects

#### Sign In (`app/auth/signin/page.tsx`)
- User login form
- Email and password authentication
- Error handling for invalid credentials
- Support for URL messages (e.g., from signup)
- Links to forgot password and sign up

#### Forgot Password (`app/auth/forgot-password/page.tsx`)
- Password reset request form
- Email validation
- Success confirmation
- Integration with Supabase password reset

#### Reset Password (`app/auth/reset-password/page.tsx`)
- Password reset form with token validation
- New password and confirmation fields
- Token validation from URL parameters
- Success states and redirects

### 5. Landing Page (`app/page.tsx`)
- Clean landing page without marketing content
- Feature highlights
- Call-to-action buttons for sign up/sign in
- Automatic redirect for authenticated users

## Usage

### Protecting Routes

```tsx
import { AuthGuard } from '@/lib/middleware/auth-guard'

export default function ProtectedPage() {
  return (
    <AuthGuard requireAuth={true}>
      {/* Your protected content */}
    </AuthGuard>
  )
}
```

### Using Authentication Context

```tsx
import { useAuth } from '@/lib/contexts/auth-context'

export default function MyComponent() {
  const { user, profile, signOut, loading } = useAuth()
  
  if (loading) return <div>Loading...</div>
  
  return (
    <div>
      <p>Welcome, {profile?.full_name || user?.email}!</p>
      <button onClick={signOut}>Sign Out</button>
    </div>
  )
}
```

### Updating User Profile

```tsx
import { useAuth } from '@/lib/contexts/auth-context'

export default function ProfileForm() {
  const { updateProfile } = useAuth()
  
  const handleUpdate = async () => {
    await updateProfile({
      full_name: 'New Name',
      currency_code: 'EUR'
    })
  }
  
  return <button onClick={handleUpdate}>Update Profile</button>
}
```

## Security Features

### Row Level Security (RLS)
- All database tables have RLS enabled
- Users can only access their own data
- Policies enforce user isolation

### Session Management
- Automatic token refresh
- Secure session storage
- Multi-device session support

### Password Security
- Minimum 6 character requirement
- Password confirmation validation
- Secure password reset flow

## Environment Variables Required

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Testing the Authentication System

1. **Sign Up Flow**
   - Visit `/auth/signup`
   - Create a new account
   - Check email for confirmation
   - Confirm account via email link

2. **Sign In Flow**
   - Visit `/auth/signin`
   - Sign in with credentials
   - Should redirect to `/dashboard`

3. **Protected Routes**
   - Try accessing `/dashboard` without authentication
   - Should redirect to `/auth/signin`

4. **Password Reset**
   - Visit `/auth/forgot-password`
   - Enter email address
   - Check email for reset link
   - Follow link to reset password

## Integration with Database

The authentication system is fully integrated with the database schema:

- User profiles are automatically created on signup
- Default categories are added for new users
- User settings are initialized with defaults
- All user data is properly isolated using RLS

## Next Steps

After authentication is working:
1. Complete user onboarding flow
2. Implement profile management in settings
3. Add social authentication providers (optional)
4. Implement email verification requirements
5. Add two-factor authentication (optional)
