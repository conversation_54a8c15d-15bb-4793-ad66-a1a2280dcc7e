'use client'

import { AppLayout } from '@/components/layouts/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Target, Plus, TrendingUp, Calendar } from 'lucide-react'

export default function GoalsPage() {
  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Financial Goals</h1>
            <p className="text-gray-600">
              Set and track your savings goals to achieve your financial dreams
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Goal
          </Button>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Savings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">$0.00</div>
              <p className="text-xs text-gray-500">Across all goals</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Goals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">0</div>
              <p className="text-xs text-gray-500">Currently tracking</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Completed Goals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">0</div>
              <p className="text-xs text-gray-500">Successfully achieved</p>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Your Goals</CardTitle>
            <CardDescription>
              Track your progress towards financial milestones
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No goals set yet
              </h3>
              <p className="text-gray-600 mb-4">
                Create your first financial goal to start tracking your progress
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Goal
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Goal Ideas</CardTitle>
            <CardDescription>
              Popular financial goals to get you started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                {
                  title: 'Emergency Fund',
                  description: '3-6 months of expenses',
                  icon: '🛡️',
                  color: 'bg-red-50 text-red-700'
                },
                {
                  title: 'Vacation Fund',
                  description: 'Save for your dream trip',
                  icon: '✈️',
                  color: 'bg-blue-50 text-blue-700'
                },
                {
                  title: 'House Down Payment',
                  description: '20% of home value',
                  icon: '🏠',
                  color: 'bg-green-50 text-green-700'
                },
                {
                  title: 'New Car',
                  description: 'Save for vehicle purchase',
                  icon: '🚗',
                  color: 'bg-purple-50 text-purple-700'
                },
                {
                  title: 'Retirement',
                  description: 'Long-term savings',
                  icon: '🏖️',
                  color: 'bg-orange-50 text-orange-700'
                },
                {
                  title: 'Education Fund',
                  description: 'Invest in learning',
                  icon: '🎓',
                  color: 'bg-indigo-50 text-indigo-700'
                }
              ].map((goal) => (
                <Card key={goal.title} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${goal.color}`}>
                        <span className="text-xl">{goal.icon}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{goal.title}</h4>
                        <p className="text-sm text-gray-600">{goal.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
