'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { getUserCategories, createCategory } from '@/lib/supabase/queries'
import { Category } from '@/lib/types/database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, Tag, Loader2, ArrowLeft, Trash2 } from 'lucide-react'

interface CategoryManagementProps {
  onComplete: () => void
  onBack: () => void
}

const CATEGORY_COLORS = [
  '#EF4444', '#F59E0B', '#10B981', '#3B82F6', '#8B5CF6', 
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
]

export function CategoryManagement({ onComplete, onBack }: CategoryManagementProps) {
  const { user } = useAuth()
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [newCategory, setNewCategory] = useState({
    name: '',
    color: CATEGORY_COLORS[0],
  })

  useEffect(() => {
    loadCategories()
  }, [user])

  const loadCategories = async () => {
    if (!user) return

    try {
      const userCategories = await getUserCategories(user.id)
      setCategories(userCategories)
    } catch (err) {
      console.error('Error loading categories:', err)
      setError('Failed to load categories')
    } finally {
      setLoading(false)
    }
  }

  const handleAddCategory = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newCategory.name.trim()) {
      setError('Category name is required')
      return
    }

    if (!user) return

    setSaving(true)
    setError('')

    try {
      const category = await createCategory({
        user_id: user.id,
        name: newCategory.name.trim(),
        color: newCategory.color,
        icon: 'Tag',
        is_default: false,
      })

      setCategories(prev => [...prev, category])
      setNewCategory({ name: '', color: CATEGORY_COLORS[0] })
    } catch (err) {
      console.error('Error creating category:', err)
      setError('Failed to create category')
    } finally {
      setSaving(false)
    }
  }

  const incomeCategories = categories.filter(cat => 
    ['Salary', 'Freelance', 'Investment', 'Other Income'].includes(cat.name)
  )
  
  const expenseCategories = categories.filter(cat => 
    !['Salary', 'Freelance', 'Investment', 'Other Income'].includes(cat.name)
  )

  const handleContinue = () => {
    onComplete()
  }

  if (loading) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Tag className="w-5 h-5" />
          Category Management
        </CardTitle>
        <CardDescription>
          Organize your transactions with categories. Default categories have been created for you, and you can add custom ones.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="view" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="view">View Categories</TabsTrigger>
            <TabsTrigger value="add">Add Custom Category</TabsTrigger>
          </TabsList>

          <TabsContent value="view" className="space-y-6">
            {/* Income Categories */}
            <div>
              <h3 className="text-lg font-semibold text-green-700 mb-3">Income Categories</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {incomeCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center space-x-2 p-3 border rounded-lg bg-green-50"
                  >
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color || '#10B981' }}
                    />
                    <span className="text-sm font-medium">{category.name}</span>
                    {category.is_default && (
                      <Badge variant="secondary" className="text-xs">Default</Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Expense Categories */}
            <div>
              <h3 className="text-lg font-semibold text-red-700 mb-3">Expense Categories</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {expenseCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center space-x-2 p-3 border rounded-lg bg-red-50"
                  >
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color || '#EF4444' }}
                    />
                    <span className="text-sm font-medium">{category.name}</span>
                    {category.is_default && (
                      <Badge variant="secondary" className="text-xs">Default</Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">What's Next?</h4>
              <p className="text-sm text-blue-700">
                You can always add, edit, or delete categories later in the Settings. 
                These categories will help you organize and track your spending patterns.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="add" className="space-y-6">
            <form onSubmit={handleAddCategory} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="categoryName">Category Name</Label>
                <Input
                  id="categoryName"
                  value={newCategory.name}
                  onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter category name"
                  disabled={saving}
                />
              </div>

              <div className="space-y-2">
                <Label>Category Color</Label>
                <div className="flex flex-wrap gap-2">
                  {CATEGORY_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 rounded-full border-2 ${
                        newCategory.color === color ? 'border-gray-900' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setNewCategory(prev => ({ ...prev, color }))}
                      disabled={saving}
                    />
                  ))}
                </div>
              </div>

              <Button type="submit" disabled={saving || !newCategory.name.trim()}>
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Category
                  </>
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            disabled={saving}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          
          <Button onClick={handleContinue} disabled={saving}>
            Continue to Dashboard
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
