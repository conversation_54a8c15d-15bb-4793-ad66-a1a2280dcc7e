'use client'

import { useAuth } from '@/lib/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Sparkles, TrendingUp, <PERSON><PERSON>hart, Target, MessageSquare } from 'lucide-react'

interface OnboardingCompleteProps {
  onComplete: () => void
}

export function OnboardingComplete({ onComplete }: OnboardingCompleteProps) {
  const { profile } = useAuth()

  const features = [
    {
      icon: TrendingUp,
      title: 'Smart Analytics',
      description: 'Get AI-powered insights into your spending patterns and financial health'
    },
    {
      icon: Pie<PERSON>hart,
      title: 'Automatic Categorization',
      description: 'Upload receipts and let AI automatically categorize your expenses'
    },
    {
      icon: Target,
      title: 'Goal Tracking',
      description: 'Set financial goals and track your progress with visual indicators'
    },
    {
      icon: MessageSquare,
      title: 'AI Financial Advisor',
      description: 'Chat with your personal AI advisor for financial guidance and tips'
    }
  ]

  return (
    <Card className="max-w-3xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="relative">
            <CheckCircle className="w-16 h-16 text-green-500" />
            <Sparkles className="w-6 h-6 text-yellow-500 absolute -top-1 -right-1" />
          </div>
        </div>
        <CardTitle className="text-2xl">
          Welcome to Personal Finance Tracker, {profile?.full_name?.split(' ')[0] || 'there'}! 🎉
        </CardTitle>
        <CardDescription className="text-lg">
          Your account is all set up and ready to help you take control of your finances
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* Setup Summary */}
        <div className="bg-green-50 p-6 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-4">Setup Complete ✅</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Profile configured</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Currency set to {profile?.currency_code}</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Categories organized</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Ready to track finances</span>
            </div>
          </div>
        </div>

        {/* Features Overview */}
        <div>
          <h3 className="text-xl font-semibold mb-4">What you can do now:</h3>
          <div className="grid md:grid-cols-2 gap-4">
            {features.map((feature, index) => (
              <div key={index} className="flex space-x-3 p-4 border rounded-lg">
                <feature.icon className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h4 className="font-medium">{feature.title}</h4>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-4">Recommended Next Steps:</h3>
          <div className="space-y-3 text-sm">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
              <div>
                <p className="font-medium">Add your first transaction</p>
                <p className="text-blue-700">Start tracking by adding income or expenses manually</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
              <div>
                <p className="font-medium">Try the AI Chat feature</p>
                <p className="text-blue-700">Upload a receipt or ask for financial advice</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
              <div>
                <p className="font-medium">Set up recurring payments</p>
                <p className="text-blue-700">Add your bills and subscriptions for automatic tracking</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">4</div>
              <div>
                <p className="font-medium">Create your first financial goal</p>
                <p className="text-blue-700">Set savings targets and track your progress</p>
              </div>
            </div>
          </div>
        </div>

        {/* Pro Tips */}
        <div className="bg-yellow-50 p-6 rounded-lg">
          <h3 className="font-semibold text-yellow-900 mb-4">💡 Pro Tips:</h3>
          <ul className="space-y-2 text-sm text-yellow-800">
            <li>• Upload receipts to the AI Chat for automatic transaction creation</li>
            <li>• Check your dashboard regularly to stay on top of your finances</li>
            <li>• Use the AI advisor for personalized financial recommendations</li>
            <li>• Set up goals to stay motivated and track your progress</li>
          </ul>
        </div>

        {/* Action Button */}
        <div className="text-center pt-4">
          <Button 
            onClick={onComplete} 
            size="lg" 
            className="px-8 py-3 text-lg"
          >
            Go to Dashboard
          </Button>
          <p className="text-sm text-gray-500 mt-2">
            You can always update your preferences in Settings
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
