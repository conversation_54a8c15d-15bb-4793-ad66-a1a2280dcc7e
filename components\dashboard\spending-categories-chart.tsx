'use client'

import * as React from "react"
import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface CategoryData {
  category: string
  amount: number
  color: string
  percentage: number
}

const COLORS = [
  "hsl(var(--chart-1))",
  "hsl(var(--chart-2))",
  "hsl(var(--chart-3))",
  "hsl(var(--chart-4))",
  "hsl(var(--chart-5))",
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff7300",
  "#00ff00",
]

export function SpendingCategoriesChart() {
  const { user, profile } = useAuth()
  const [categoryData, setCategoryData] = useState<CategoryData[]>([])
  const [loading, setLoading] = useState(true)
  const [totalSpending, setTotalSpending] = useState(0)

  const currency = profile?.currency_code || 'USD'

  useEffect(() => {
    if (user) {
      loadCategoryData()
    }
  }, [user])

  const loadCategoryData = async () => {
    if (!user) return

    setLoading(true)
    try {
      // Generate sample data for now - in real implementation, this would fetch from Supabase
      const data = generateSampleCategoryData()
      setCategoryData(data)
      setTotalSpending(data.reduce((sum, item) => sum + item.amount, 0))
    } catch (error) {
      console.error('Error loading category data:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateSampleCategoryData = (): CategoryData[] => {
    const categories = [
      { name: 'Food & Dining', amount: 850, color: '#EF4444' },
      { name: 'Transportation', amount: 420, color: '#F59E0B' },
      { name: 'Shopping', amount: 320, color: '#EC4899' },
      { name: 'Bills & Utilities', amount: 680, color: '#6B7280' },
      { name: 'Entertainment', amount: 240, color: '#8B5CF6' },
      { name: 'Healthcare', amount: 180, color: '#EF4444' },
      { name: 'Home & Garden', amount: 150, color: '#F59E0B' },
      { name: 'Personal Care', amount: 120, color: '#EC4899' },
    ]

    const total = categories.reduce((sum, cat) => sum + cat.amount, 0)

    return categories.map((cat, index) => ({
      category: cat.name,
      amount: cat.amount,
      color: COLORS[index] || cat.color,
      percentage: Math.round((cat.amount / total) * 100),
    }))
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const chartConfig = categoryData.reduce((config, item, index) => {
    config[item.category.toLowerCase().replace(/\s+/g, '_')] = {
      label: item.category,
      color: item.color,
    }
    return config
  }, {} as ChartConfig)

  if (loading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Spending by Category</CardTitle>
          <CardDescription>Loading category breakdown...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full animate-pulse bg-gray-200 rounded"></div>
        </CardContent>
      </Card>
    )
  }

  if (categoryData.length === 0) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Spending by Category</CardTitle>
          <CardDescription>This month's expense breakdown</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">📊</div>
              <p>No spending data available</p>
              <p className="text-sm">Add some transactions to see your spending breakdown</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Spending by Category</CardTitle>
        <CardDescription>
          This month's expense breakdown • Total: {formatCurrency(totalSpending)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pie Chart */}
          <div className="flex items-center justify-center">
            <ChartContainer
              config={chartConfig}
              className="h-[300px] w-full"
            >
              <PieChart>
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      formatter={(value, name) => [
                        formatCurrency(value as number),
                        name,
                      ]}
                      indicator="dot"
                    />
                  }
                />
                <Pie
                  data={categoryData}
                  dataKey="amount"
                  nameKey="category"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  innerRadius={40}
                  paddingAngle={2}
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
              </PieChart>
            </ChartContainer>
          </div>

          {/* Legend */}
          <div className="space-y-3">
            <h4 className="font-semibold text-sm text-gray-700">Category Breakdown</h4>
            <div className="space-y-2 max-h-[250px] overflow-y-auto">
              {categoryData
                .sort((a, b) => b.amount - a.amount)
                .map((item, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-sm font-medium text-gray-700">
                        {item.category}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-gray-900">
                        {formatCurrency(item.amount)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.percentage}%
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
