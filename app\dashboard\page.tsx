'use client'

import { AppLayout } from "@/components/layouts/app-layout"
import { ChartAreaInteractive } from "@/components/chart-area-interactive"
import { DataTable } from "@/components/data-table"
import { SectionCards } from "@/components/section-cards"

import data from "./data.json"

export default function Page() {
  return (
    <AppLayout>
      <div className="max-w-7xl mx-auto">
        <SectionCards />
        <div className="mb-6">
          <ChartAreaInteractive />
        </div>
        <DataTable data={data} />
      </div>
    </AppLayout>
  )
}
