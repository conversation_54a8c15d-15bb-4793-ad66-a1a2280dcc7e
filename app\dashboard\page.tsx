'use client'

import { AppLayout } from "@/components/layouts/app-layout"
import { FinancialSummaryCards } from "@/components/dashboard/financial-summary-cards"
import { CashFlowChart } from "@/components/dashboard/cash-flow-chart"
import { SpendingCategoriesChart } from "@/components/dashboard/spending-categories-chart"
import { GoalsProgress } from "@/components/dashboard/goals-progress"
import { RecentTransactions } from "@/components/dashboard/recent-transactions"
import { UpcomingPayments } from "@/components/dashboard/upcoming-payments"

export default function Page() {
  return (
    <AppLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Financial Summary Cards */}
        <FinancialSummaryCards />

        {/* Main Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CashFlowChart />
          <SpendingCategoriesChart />
        </div>

        {/* Secondary Information Row */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <GoalsProgress />
          <RecentTransactions />
          <UpcomingPayments />
        </div>
      </div>
    </AppLayout>
  )
}
