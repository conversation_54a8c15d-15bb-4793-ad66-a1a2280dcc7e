'use client'

import { AppLayout } from '@/components/layouts/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Receipt, Plus, Search, Filter } from 'lucide-react'

export default function TransactionsPage() {
  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto">
                    <div className="flex justify-between items-center mb-8">
                      <div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Transactions</h1>
                        <p className="text-gray-600">
                          Manage all your financial transactions in one place
                        </p>
                      </div>
                      <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Transaction
                      </Button>
                    </div>

                    <div className="grid md:grid-cols-4 gap-6 mb-8">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-gray-600">Total Income</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-green-600">$0.00</div>
                          <p className="text-xs text-gray-500">This month</p>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-gray-600">Total Expenses</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-red-600">$0.00</div>
                          <p className="text-xs text-gray-500">This month</p>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-gray-600">Net Income</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-blue-600">$0.00</div>
                          <p className="text-xs text-gray-500">This month</p>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-gray-600">Transactions</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-gray-900">0</div>
                          <p className="text-xs text-gray-500">This month</p>
                        </CardContent>
                      </Card>
                    </div>

                    <Card>
                      <CardHeader>
                        <div className="flex justify-between items-center">
                          <div>
                            <CardTitle>Transaction History</CardTitle>
                            <CardDescription>
                              View and manage all your financial transactions
                            </CardDescription>
                          </div>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              <Search className="mr-2 h-4 w-4" />
                              Search
                            </Button>
                            <Button variant="outline" size="sm">
                              <Filter className="mr-2 h-4 w-4" />
                              Filter
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-12">
                          <Receipt className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            No transactions yet
                          </h3>
                          <p className="text-gray-600 mb-4">
                            Start tracking your finances by adding your first transaction
                          </p>
                          <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Your First Transaction
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
      </div>
    </AppLayout>
  )
}
