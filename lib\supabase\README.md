# Supabase Database Setup

This directory contains all the necessary files to set up the Supabase database for the Personal Finance Tracker application.

## Setup Instructions

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new account or sign in
2. Click "New Project"
3. Choose your organization
4. Enter project name: "Personal Finance Tracker"
5. Enter a secure database password
6. Choose your region (closest to your users)
7. Click "Create new project"

### 2. Get Your Project Credentials

Once your project is created:

1. Go to Settings → API
2. Copy the following values:
   - **Project URL** (under "Project URL")
   - **anon public** key (under "Project API keys")
   - **service_role** key (under "Project API keys") - Keep this secret!

### 3. Update Environment Variables

Update your `.env.local` file with the actual values:

```env
NEXT_PUBLIC_SUPABASE_URL=your_actual_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key
```

### 4. Run Database Migrations

Execute the SQL files in the following order in your Supabase SQL Editor:

1. **schema.sql** - Creates all tables, indexes, and triggers
2. **rls-policies.sql** - Sets up Row Level Security policies
3. **default-categories.sql** - Creates default categories and user setup functions

#### To run the SQL files:

1. Go to your Supabase dashboard
2. Click on "SQL Editor" in the left sidebar
3. Click "New query"
4. Copy and paste the contents of each SQL file
5. Click "Run" to execute

### 5. Verify Setup

After running all SQL files, verify your setup:

1. Go to "Table Editor" in your Supabase dashboard
2. You should see the following tables:
   - `user_profiles`
   - `categories`
   - `transactions`
   - `recurring_payments`
   - `goals`
   - `loans`
   - `ai_conversations`
   - `user_settings`

3. Go to "Authentication" → "Policies"
4. Verify that RLS is enabled on all tables and policies are created

## Database Schema Overview

### Core Tables

- **user_profiles**: Extended user information beyond Supabase auth
- **categories**: Transaction categories (income/expense)
- **transactions**: All financial transactions
- **recurring_payments**: Subscriptions and recurring bills
- **goals**: Financial goals and savings targets
- **loans**: Bank loans and personal debts
- **ai_conversations**: Chat history with AI assistant
- **user_settings**: User preferences and dashboard configuration

### Security Features

- **Row Level Security (RLS)**: Enabled on all tables
- **User Isolation**: Users can only access their own data
- **Automatic User Setup**: New users get default categories and settings

### Key Features

- **Multi-currency Support**: All monetary fields support different currencies
- **Automatic Timestamps**: Created/updated timestamps on all records
- **Referential Integrity**: Proper foreign key relationships
- **Performance Optimized**: Indexes on frequently queried columns

## Usage in Application

The database is accessed through:

- `lib/supabase/client.ts` - Supabase client configuration
- `lib/supabase/queries.ts` - Pre-built query functions
- `lib/types/database.ts` - TypeScript type definitions

## Testing the Setup

You can test your database setup by:

1. Creating a test user through Supabase Auth
2. Verifying default categories are created
3. Testing CRUD operations on transactions
4. Ensuring RLS policies prevent cross-user data access

## Troubleshooting

### Common Issues

1. **Environment variables not loading**: Make sure `.env.local` is in the root directory
2. **RLS blocking queries**: Ensure user is authenticated and policies are correct
3. **Foreign key errors**: Make sure referenced records exist (e.g., categories before transactions)

### Support

If you encounter issues:
1. Check the Supabase dashboard logs
2. Verify your environment variables
3. Ensure all SQL files were executed successfully
4. Check that RLS policies are properly configured
