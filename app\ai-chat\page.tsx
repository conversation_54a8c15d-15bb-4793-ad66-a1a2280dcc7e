'use client'

import { AppLayout } from '@/components/layouts/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageSquare, Upload, Zap } from 'lucide-react'

export default function AIChatPage() {
  return (
    <AppLayout>
      <div className="max-w-4xl mx-auto">
                    <div className="mb-8">
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">AI Financial Assistant</h1>
                      <p className="text-gray-600">
                        Chat with your personal AI advisor for financial guidance, upload receipts for automatic processing, and get insights into your spending patterns.
                      </p>
                    </div>

                    <div className="grid md:grid-cols-3 gap-6 mb-8">
                      <Card>
                        <CardHeader>
                          <MessageSquare className="h-8 w-8 text-blue-600 mb-2" />
                          <CardTitle className="text-lg">Financial Advice</CardTitle>
                          <CardDescription>
                            Get personalized financial recommendations and budgeting tips
                          </CardDescription>
                        </CardHeader>
                      </Card>

                      <Card>
                        <CardHeader>
                          <Upload className="h-8 w-8 text-green-600 mb-2" />
                          <CardTitle className="text-lg">Receipt Processing</CardTitle>
                          <CardDescription>
                            Upload receipts and let AI automatically extract transaction details
                          </CardDescription>
                        </CardHeader>
                      </Card>

                      <Card>
                        <CardHeader>
                          <Zap className="h-8 w-8 text-purple-600 mb-2" />
                          <CardTitle className="text-lg">Smart Insights</CardTitle>
                          <CardDescription>
                            Discover spending patterns and get actionable financial insights
                          </CardDescription>
                        </CardHeader>
                      </Card>
                    </div>

                    <Card>
                      <CardContent className="p-8">
                        <div className="text-center">
                          <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            AI Chat Coming Soon
                          </h3>
                          <p className="text-gray-600 mb-4">
                            The AI chat interface with OCR integration is currently under development.
                            This will include:
                          </p>
                          <div className="text-left max-w-md mx-auto space-y-2">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                              <span className="text-sm">Conversational financial advice</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                              <span className="text-sm">Receipt upload and processing</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                              <span className="text-sm">Automatic transaction creation</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                              <span className="text-sm">Smart categorization suggestions</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
      </div>
    </AppLayout>
  )
}
