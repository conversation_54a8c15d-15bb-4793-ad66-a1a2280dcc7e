================================================================
RepopackPy Output File
================================================================

This file was generated by RepopackPy on: 2025-07-15T01:45:22.973159

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This header section
2. Repository structure
3. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
1. This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
2. When processing this file, use the separators and "File:" markers to
  distinguish between different files in the repository.
3. Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and RepopackPy's
  configuration.
- Binary files are not included in this packed representation. Please refer to
  the Repository Structure section for a complete list of file paths, including
  binary files.

For more information about RepopackPy, visit: https://github.com/abinthomasonline/repopack-py

================================================================
Repository Structure
================================================================
.env.local.examples
README.md
app\api\chat\route.ts
app\api\parse-pdf\route.ts
app\favicon.ico
app\globals.css
app\layout.tsx
app\page.tsx
components.json
components\chat-interface.tsx
components\error-display.tsx
components\file-uploader.tsx
components\info-panel.tsx
components\markdown.tsx
components\processing-indicator.tsx
components\results-viewer.tsx
components\sample-pdf-option.tsx
components\theme-provider.tsx
components\theme-toggle.tsx
components\ui\alert.tsx
components\ui\button.tsx
components\ui\card.tsx
components\ui\input.tsx
components\ui\progress.tsx
components\ui\tabs.tsx
components\ui\tooltip.tsx
hooks\use-window-size.ts
lib\server\asset-store.ts
lib\utils.ts
next.config.mjs
next.config.ts
package.json
postcss.config.mjs
public\file.svg
public\globe.svg
public\next.svg
public\vercel.svg
public\window.svg
tailwind.config.js
tsconfig.json

================================================================
Repository Files
================================================================

================
File: .env.local.examples
================
# Mistral API Key

MISTRAL_API_KEY=your_mistral_api_key

OPENAI_API_KEY=your_openai_api_key

ANTHROPIC_API_KEY=your_anthropic_api_key

BLOB_READ_WRITE_TOKEN="your_blob_read_write_token"

GOOGLE_GENERATIVE_AI_API_KEY=

================
File: components.json
================
{

  "$schema": "https://ui.shadcn.com/schema.json",

  "style": "default",

  "rsc": true,

  "tsx": true,

  "tailwind": {

    "config": "tailwind.config.ts",

    "css": "app/globals.css",

    "baseColor": "neutral",

    "cssVariables": true,

    "prefix": ""

  },

  "aliases": {

    "components": "@/components",

    "utils": "@/lib/utils",

    "ui": "@/components/ui",

    "lib": "@/lib",

    "hooks": "@/hooks"

  },

  "iconLibrary": "lucide"

}

================
File: next.config.mjs
================
let userConfig = undefined

try {

  userConfig = await import('./v0-user-next.config')

} catch (e) {

  // ignore error

}



/** @type {import('next').NextConfig} */

const nextConfig = {

  eslint: {

    ignoreDuringBuilds: true,

  },

  typescript: {

    ignoreBuildErrors: true,

  },

  images: {

    unoptimized: true,

  },

  experimental: {

    webpackBuildWorker: true,

    parallelServerBuildTraces: true,

    parallelServerCompiles: true,

  },

}



mergeConfig(nextConfig, userConfig)



function mergeConfig(nextConfig, userConfig) {

  if (!userConfig) {

    return

  }



  for (const key in userConfig) {

    if (

      typeof nextConfig[key] === 'object' &&

      !Array.isArray(nextConfig[key])

    ) {

      nextConfig[key] = {

        ...nextConfig[key],

        ...userConfig[key],

      }

    } else {

      nextConfig[key] = userConfig[key]

    }

  }

}



export default nextConfig

================
File: next.config.ts
================
import type { NextConfig } from "next";



const nextConfig: NextConfig = {

  /* config options here */

};



export default nextConfig;

================
File: package.json
================
{

  "name": "my-v0-project",

  "version": "0.1.0",

  "private": true,

  "scripts": {

    "dev": "next dev",

    "build": "next build",

    "start": "next start",

    "lint": "next lint"

  },

  "dependencies": {

    "@ai-sdk/anthropic": "^1.2.2",

    "@ai-sdk/google": "^1.2.5",

    "@ai-sdk/openai": "^1.3.0",

    "@ai-sdk/react": "^1.2.0",

    "@hookform/resolvers": "^3.9.1",

    "@mistralai/mistralai": "^1.5.2",

    "@radix-ui/react-accordion": "^1.2.2",

    "@radix-ui/react-alert-dialog": "^1.1.4",

    "@radix-ui/react-aspect-ratio": "^1.1.1",

    "@radix-ui/react-avatar": "^1.1.2",

    "@radix-ui/react-checkbox": "^1.1.3",

    "@radix-ui/react-collapsible": "^1.1.2",

    "@radix-ui/react-context-menu": "^2.2.4",

    "@radix-ui/react-dialog": "^1.1.4",

    "@radix-ui/react-dropdown-menu": "^2.1.4",

    "@radix-ui/react-hover-card": "^1.1.4",

    "@radix-ui/react-label": "^2.1.1",

    "@radix-ui/react-menubar": "^1.1.4",

    "@radix-ui/react-navigation-menu": "^1.2.3",

    "@radix-ui/react-popover": "^1.1.4",

    "@radix-ui/react-progress": "^1.1.2",

    "@radix-ui/react-radio-group": "^1.2.2",

    "@radix-ui/react-scroll-area": "^1.2.2",

    "@radix-ui/react-select": "^2.1.4",

    "@radix-ui/react-separator": "^1.1.1",

    "@radix-ui/react-slider": "^1.2.2",

    "@radix-ui/react-slot": "^1.1.2",

    "@radix-ui/react-switch": "^1.1.2",

    "@radix-ui/react-tabs": "^1.1.3",

    "@radix-ui/react-toast": "^1.2.4",

    "@radix-ui/react-toggle": "^1.1.1",

    "@radix-ui/react-toggle-group": "^1.1.1",

    "@radix-ui/react-tooltip": "^1.1.8",

    "@tailwindcss/typography": "latest",

    "@vercel/blob": "^0.19.0",

    "ai": "^4.2.0",

    "autoprefixer": "^10.4.20",

    "class-variance-authority": "^0.7.1",

    "clsx": "^2.1.1",

    "cmdk": "1.0.4",

    "date-fns": "4.1.0",

    "embla-carousel-react": "8.5.1",

    "fs": "0.0.1-security",

    "geist": "^1.3.1",

    "input-otp": "1.4.1",

    "lucide-react": "^0.454.0",

    "next": "15.1.0",

    "next-themes": "^0.4.6",

    "path": "^0.12.7",

    "react": "^19",

    "react-day-picker": "8.10.1",

    "react-dom": "^19",

    "react-dropzone": "^14.3.8",

    "react-hook-form": "^7.54.1",

    "react-markdown": "^10.1.0",

    "react-resizable-panels": "^2.1.7",

    "recharts": "2.15.0",

    "remark-gfm": "^4.0.1",

    "server-only": "^0.0.1",

    "sonner": "^1.7.1",

    "tailwind-merge": "^2.5.5",

    "tailwindcss-animate": "^1.0.7",

    "uuid": "^11.1.0",

    "vaul": "^0.9.6",

    "zod": "^3.24.2"

  },

  "devDependencies": {

    "@types/node": "^22",

    "@types/react": "^19",

    "@types/react-dom": "^19",

    "postcss": "^8",

    "tailwindcss": "^3.4.17",

    "typescript": "^5"

  }

}

================
File: postcss.config.mjs
================
/** @type {import('postcss-load-config').Config} */

const config = {

    plugins: {

      tailwindcss: {},

    },

  };

  

  export default config;

================
File: README.md
================
# Mistral OCR PDF Parser



> A powerful document processing solution that combines OCR capabilities with interactive AI chat interface.



## ðŸš€ Features



- **PDF Processing**: Extract text and images from PDF documents with layout preservation

- **OCR Integration**: Leverage Mistral's OCR technology for accurate text recognition

- **ChatPDF**: Interact with your documents through a natural language interface

- **Asset Management**: View and manage extracted images with zoom functionality



Built with Next.js, AI SDK, and Mistral API, this application streamlines document analysis workflows by providing a seamless integration between document processing and conversational AI.



Get started by uploading a PDF or trying our sample document!



## ðŸ“¹ Demo



https://github.com/user-attachments/assets/d29a6c28-0b1f-4dd7-8564-f217bedbf23e



## Getting Started



Configure the required API keys in the `.env.local` file:





```plaintext

# Required for OCR functionality

MISTRAL_API_KEY=your_mistral_api_key_here



# Required for ChatPDF functionality

ANTHROPIC_API_KEY=your_anthropic_api_key_here



# Maybe Required for Serverless deployment for Image Rendering during ChatPDF chatting session

BLOB_READ_WRITE_TOKEN="your_blob_read_write_token"

```



You need to obtain:



- A Mistral API key for the OCR functionality to process PDF documents

- An Anthropic API key for the ChatPDF feature to enable AI-powered document chat

- For image rendering, deployment to a host may be necessary. If the deployed host operates in a serverless environment without a filesystem, a separate blob storage service should be used. This project is designed to run locally without requiring a blob service.



Without these API keys, the respective features will not work properly.



Run the development server:



```bash



pnpm dev



```



Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.



You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.



This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.



## Learn More



To learn more about Next.js, take a look at the following resources:



- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.

- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.



You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!



## Deploy on Vercel



The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.



Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

================
File: tailwind.config.js
================
module.exports = {

  darkMode: ["class"],

  content: [

    "./pages/**/*.{ts,tsx}",

    "./components/**/*.{ts,tsx}",

    "./app/**/*.{ts,tsx}",

    "./src/**/*.{ts,tsx}",

    "*.{js,ts,jsx,tsx,mdx}",

  ],

  prefix: "",

  theme: {

    container: {

      center: true,

      padding: "2rem",

      screens: {

        "2xl": "1400px",

      },

    },

    extend: {

      colors: {

        border: "hsl(var(--border))",

        input: "hsl(var(--input))",

        ring: "hsl(var(--ring))",

        background: "hsl(var(--background))",

        foreground: "hsl(var(--foreground))",

        primary: {

          DEFAULT: "hsl(var(--primary))",

          foreground: "hsl(var(--primary-foreground))",

        },

        secondary: {

          DEFAULT: "hsl(var(--secondary))",

          foreground: "hsl(var(--secondary-foreground))",

        },

        destructive: {

          DEFAULT: "hsl(var(--destructive))",

          foreground: "hsl(var(--destructive-foreground))",

        },

        muted: {

          DEFAULT: "hsl(var(--muted))",

          foreground: "hsl(var(--muted-foreground))",

        },

        accent: {

          DEFAULT: "hsl(var(--accent))",

          foreground: "hsl(var(--accent-foreground))",

        },

        popover: {

          DEFAULT: "hsl(var(--popover))",

          foreground: "hsl(var(--popover-foreground))",

        },

        card: {

          DEFAULT: "hsl(var(--card))",

          foreground: "hsl(var(--card-foreground))",

        },

        // Chart colors

        chart: {

          1: "hsl(var(--chart-1))",

          2: "hsl(var(--chart-2))",

          3: "hsl(var(--chart-3))",

          4: "hsl(var(--chart-4))",

          5: "hsl(var(--chart-5))",

        },

        // School icon colors

        "school-icon": {

          DEFAULT: "hsl(var(--school-icon))",

          foreground: "hsl(var(--school-icon-foreground))",

        },

      },

      borderRadius: {

        lg: "var(--radius)",

        md: "calc(var(--radius) - 2px)",

        sm: "calc(var(--radius) - 4px)",

      },

      keyframes: {

        "accordion-down": {

          from: { height: "0" },

          to: { height: "var(--radix-accordion-content-height)" },

        },

        "accordion-up": {

          from: { height: "var(--radix-accordion-content-height)" },

          to: { height: "0" },

        },

      },

      animation: {

        "accordion-down": "accordion-down 0.2s ease-out",

        "accordion-up": "accordion-up 0.2s ease-out",

      },

      typography: {

        DEFAULT: {

          css: {

            maxWidth: "100%",

            img: {

              marginTop: "0",

              marginBottom: "0",

            },

          },

        },

      },

    },

  },

  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],

}

================
File: tsconfig.json
================
{

  "compilerOptions": {

    "lib": ["dom", "dom.iterable", "esnext"],

    "allowJs": true,

    "target": "ES6",

    "skipLibCheck": true,

    "strict": true,

    "noEmit": true,

    "esModuleInterop": true,

    "module": "esnext",

    "moduleResolution": "bundler",

    "resolveJsonModule": true,

    "isolatedModules": true,

    "jsx": "preserve",

    "incremental": true,

    "plugins": [

      {

        "name": "next"

      }

    ],

    "paths": {

      "@/*": ["./*"]

    }

  },

  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],

  "exclude": ["node_modules"]

}

================
File: app\globals.css
================
@tailwind base;

@tailwind components;

@tailwind utilities;



@layer base {

  :root {

    --background: 0 0% 100%;

    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;

    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 240 10% 3.9%;

    --primary: 142.1 76.2% 36.3%;

    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 240 4.8% 95.9%;

    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;

    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;

    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;

    --input: 240 5.9% 90%;

    --ring: 142.1 76.2% 36.3%;

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --school-icon: 47.9 95.8% 53.1%;

    --school-icon-foreground: 26 83.3% 14.1%;

  }



  .dark {

    --background: 20 14.3% 4.1%;

    --foreground: 0 0% 95%;

    --card: 24 9.8% 10%;

    --card-foreground: 0 0% 95%;

    --popover: 0 0% 9%;

    --popover-foreground: 0 0% 95%;

    --primary: 142.1 70.6% 45.3%;

    --primary-foreground: 144.9 80.4% 10%;

    --secondary: 240 3.7% 15.9%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;

    --muted-foreground: 240 5% 64.9%;

    --accent: 12 6.5% 15.1%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 0 85.7% 97.3%;

    --border: 240 3.7% 15.9%;

    --input: 240 3.7% 15.9%;

    --ring: 142.4 71.8% 29.2%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%;

    --school-icon: 47.9 95.8% 53.1%;

    --school-icon-foreground: 26 83.3% 14.1%;

  }

}



@layer base {

  * {

    @apply border-border;

  }

  body {

    @apply bg-background text-foreground;

  }

}



/* Dark mode styles for ReactMarkdown */

.dark .prose {

  --tw-prose-body: theme("colors.gray.300");

  --tw-prose-headings: theme("colors.white");

  --tw-prose-lead: theme("colors.gray.300");

  --tw-prose-links: theme("colors.blue.400");

  --tw-prose-bold: theme("colors.white");

  --tw-prose-counters: theme("colors.gray.400");

  --tw-prose-bullets: theme("colors.gray.400");

  --tw-prose-hr: theme("colors.gray.700");

  --tw-prose-quotes: theme("colors.gray.300");

  --tw-prose-quote-borders: theme("colors.gray.700");

  --tw-prose-captions: theme("colors.gray.400");

  --tw-prose-code: theme("colors.white");

  --tw-prose-pre-code: theme("colors.gray.300");

  --tw-prose-pre-bg: theme("colors.gray.800");

  --tw-prose-th-borders: theme("colors.gray.700");

  --tw-prose-td-borders: theme("colors.gray.700");

}



/* Custom school icon button styles */

.school-icon-button {

  background-color: hsl(var(--school-icon));

  color: hsl(var(--school-icon-foreground));

}



.school-icon-button:hover {

  background-color: hsl(var(--school-icon) / 0.9);

}



.school-icon-button-outline {

  color: hsl(var(--foreground));

  border: 1px solid hsl(var(--border));

}



.school-icon-button-outline:hover {

  background-color: hsl(var(--school-icon) / 0.1);

  border-color: hsl(var(--school-icon));

}

================
File: app\layout.tsx
================
import type React from "react"

import type { Metadata } from "next"

import { GeistMono } from "geist/font/mono"

import "./globals.css"

import { ThemeProvider } from "@/components/theme-provider"



const geistMono = GeistMono.className



export const metadata: Metadata = {

  title: "Mistral OCR PDF Parser",

  description: "Parse PDF documents using Mistral OCR and visualize the results",

}



export default function RootLayout({

  children,

}: Readonly<{

  children: React.ReactNode

}>) {

  return (

    <html lang="en" suppressHydrationWarning>

      <body className={geistMono}>

        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>

          {children}

        </ThemeProvider>

      </body>

    </html>

  )

}

================
File: app\page.tsx
================
"use client"



import { useState } from "react"

import { FileUploader } from "@/components/file-uploader"

import { ProcessingIndicator } from "@/components/processing-indicator"

import { ResultsViewer } from "@/components/results-viewer"

import { InfoPanel } from "@/components/info-panel"

import { ErrorDisplay } from "@/components/error-display"

import { SamplePdfOption } from "@/components/sample-pdf-option"

import { Button } from "@/components/ui/button"

import { School } from "lucide-react"

import { ThemeToggle } from "@/components/theme-toggle"



// ResultsViewer props와 일치하는 인터페이스 정의

interface ImageData {

  id: string

  url: string

  coordinates: { x: number; y: number; width: number; height: number }

  originalCoordinates: {

    top_left_x: number

    top_left_y: number

    bottom_right_x: number

    bottom_right_y: number

  }

}



interface PageData {

  index: number

  markdown: string

  rawMarkdown: string

  images: ImageData[]

  dimensions: {

    dpi: number

    height: number

    width: number

  }

}



interface ResultsData {

  text: string

  rawText: string

  pages: PageData[]

  images: ImageData[]

  usage?: {

    pages_processed: number

    doc_size_bytes: number

  }

  model?: string

}



export default function Home() {

  const [file, setFile] = useState<File | null>(null)

  const [isSample, setIsSample] = useState(false)

  const [processingStage, setProcessingStage] = useState<"uploading" | "processing" | "extracting" | null>(null)

  const [results, setResults] = useState<ResultsData | null>(null)

  const [error, setError] = useState<{ message: string; details?: string } | null>(null)

  const [showInfo, setShowInfo] = useState(false)



  const handleFileSelected = (selectedFile: File, fileIsSample = false) => {

    setFile(selectedFile)

    setIsSample(fileIsSample)

    setResults(null)

    setError(null)

  }



  const handleProcessFile = async () => {

    if (!file) return



    setError(null)

    setProcessingStage("uploading")



    const formData = new FormData()

    formData.append("pdf", file)

    formData.append("isSample", isSample.toString())



    try {

      // 업로드 단계 시뮬레이션

      await new Promise((resolve) => setTimeout(resolve, 1500))

      setProcessingStage("processing")



      // 처리 단계 시뮬레이션

      await new Promise((resolve) => setTimeout(resolve, 2000))

      setProcessingStage("extracting")



      const response = await fetch("/api/parse-pdf", {

        method: "POST",

        body: formData,

      })



      const data = await response.json()



      if (!response.ok) {

        throw new Error(data.message || data.error || "Failed to process PDF")

      }



      setResults(data)

    } catch (error) {

      console.error("Error processing PDF:", error)



      // 더 자세한 오류 정보 추출

      let errorMessage = error instanceof Error ? error.message : "An unexpected error occurred"

      let errorDetails = error instanceof Error ? error.stack : undefined



      // 오류가 API에서 발생했고 세부 정보가 있는 경우

      if (error instanceof Error && error.message.includes("Failed to process PDF")) {

        try {

          // 수정: 정규식에서 줄바꿈 문제 해결

          const errorStack = error.stack || ""

          const bodyMatch = errorStack.match(/body: (.+?)(?:\n|$)/)



          if (bodyMatch && bodyMatch[1]) {

            const errorBody = JSON.parse(bodyMatch[1])

            errorMessage = errorBody.message || errorBody.error || errorMessage

            errorDetails = errorBody.details || errorBody.stack || JSON.stringify(errorBody)

          }

        } catch (e) {

          console.error("Failed to parse error details:", e)

        }

      }



      setError({

        message: errorMessage,

        details: errorDetails,

      })

    } finally {

      setProcessingStage(null)

    }

  }



  const toggleInfoPanel = () => {

    setShowInfo(!showInfo)

  }



  const formatBytes = (bytes: number, decimals = 2) => {

    if (bytes === 0) return "0 Bytes"



    const k = 1024

    const dm = decimals < 0 ? 0 : decimals

    const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]



    const i = Math.floor(Math.log(bytes) / Math.log(k))



    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i]

  }



  return (

    <main className="min-h-screen p-4 md:p-8 bg-background">

      <div className="max-w-7xl mx-auto">

        <div className="flex justify-between items-center mb-8">

          <h1 className="text-3xl font-bold">Mistral OCR PDF Parser</h1>

          <div className="flex items-center gap-2">

            <Button

              variant="ghost"

              size="icon"

              onClick={toggleInfoPanel}

              aria-label="Show information"

              className={showInfo ? "bg-accent" : ""}

            >

              <School className="h-5 w-5" />

            </Button>

            <ThemeToggle />

          </div>

        </div>



        {showInfo && <InfoPanel onClose={toggleInfoPanel} />}



        <div className="grid grid-cols-1 lg:grid-cols-9 gap-8">

          <div className="space-y-6 lg:col-span-2">

            <div className="bg-card rounded-lg p-6 shadow-sm">

              <h2 className="text-xl font-semibold mb-4">Upload PDF</h2>

              <FileUploader onFileSelected={(file) => handleFileSelected(file, false)} />



              <SamplePdfOption onSelect={handleFileSelected} />



              {file && !processingStage && (

                <div className="mt-4">

                  <p className="text-sm text-muted-foreground mb-2">

                    Selected file: {file.name}

                    {isSample && " (Sample)"}

                  </p>

                  <Button onClick={handleProcessFile} className="w-full">

                    Process PDF

                  </Button>

                </div>

              )}



              {results && results.usage && (

                <div className="mt-4 bg-muted/30 p-3 rounded-md text-xs text-muted-foreground">

                  <div className="space-y-1">

                    <div className="flex justify-between">

                      <span>Model:</span>

                      <span>{results.model || "mistral-ocr-latest"}</span>

                    </div>

                    <div className="flex justify-between">

                      <span>Pages processed:</span>

                      <span>{results.usage.pages_processed}</span>

                    </div>

                    <div className="flex justify-between">

                      <span>Document size:</span>

                      <span>{formatBytes(results.usage.doc_size_bytes)}</span>

                    </div>

                  </div>

                </div>

              )}

            </div>



            {processingStage && (

              <div className="bg-card rounded-lg p-6 shadow-sm">

                <ProcessingIndicator stage={processingStage} />

              </div>

            )}



            {error && (

              <div className="bg-card rounded-lg p-6 shadow-sm">

                <ErrorDisplay message={error.message} details={error.details} onRetry={handleProcessFile} />

              </div>

            )}

          </div>



          <div className="bg-card rounded-lg p-6 shadow-sm lg:col-span-7">

            {results ? (

              <ResultsViewer results={results} originalFile={file} />

            ) : (

              <div>

                <h2 className="text-xl font-semibold mb-4">Results</h2>

                <div className="flex flex-col items-center justify-center h-64 text-center">

                  <p className="text-muted-foreground">Upload and process a PDF to see the parsed results</p>

                </div>

              </div>

            )}

          </div>

        </div>

      </div>

    </main>

  )

}

================
File: app\api\chat\route.ts
================
import { anthropic } from "@ai-sdk/anthropic"

import { createDataStreamResponse, streamText, tool } from "ai"

import { z } from "zod"

import { google } from '@ai-sdk/google';



export async function POST(req: Request) {

  const { messages, documentContent } = await req.json()

  let stepCounter = 0



  const systemMessage = documentContent

    ? `You are a helpful assistant that answers questions about the following document content. 

       Use this content to provide accurate answers:

       

       ${documentContent}

       

       Ensure that the 'ExtractSubject' tool is used for the user's first message.

       For data visualization:

- Proactively apply visualizations whenever possible.

- Use <img alt="Extracted image 1" class="w-full h-full object-contain" src="/assets/ocr-images/...jpeg"> 

  for image suggestions.

       `

    : "You are a helpful assistant."



  return createDataStreamResponse({

    execute: async (dataStream) => {

      const result = streamText({

        // model: anthropic("claude-3-5-sonnet-latest"),

        // providerOptions: {

        //   anthropic: {

        //     cache_control: {

        //       type: "ephemeral",

        //     },

        //   },

        // },

        model: google('gemini-2.5-pro-exp-03-25'),

        system: systemMessage,

        messages,

        toolCallStreaming: true,

        tools: {

          ExtractSubject: tool({

            description: "Extracts a subject from the context injected into the system prompt.",

            parameters: z.object({ subject: z.string() }),

            execute: async ({ subject }) => subject, // no-op extract tool

          }),

        },

        maxSteps: 3,

        onStepFinish: ({ toolCalls, toolResults, finishReason, usage, text }) => {

          stepCounter++

          console.log(`📊 Step ${stepCounter} Finished:`)

          console.log("🏁 Finish Reason:", finishReason)

          console.log("💬 Model Response:", text)



          if (toolCalls && toolCalls.length > 0) {

            console.log("🛠️ Tool Calls:")

            toolCalls.forEach((call, index) => {

              console.log(`  [${index + 1}] Tool: ${call.toolName}, Arguments:`, call.args)

            })

          }



          if (toolResults && toolResults.length > 0) {

            console.log("🔧 Tool Results:")

            toolResults.forEach((result, index) => {

              console.log(`  [${index + 1}] Result:`, typeof result === "object" ? JSON.stringify(result) : result)

            })

          }



          if (usage) {

            console.log("📈 Usage:", usage)

          }



          console.log("------------------------")

        },

      })



      result.mergeIntoDataStream(dataStream)

    },

  })

}

================
File: app\api\parse-pdf\route.ts
================
import { type NextRequest, NextResponse } from "next/server"

import { Mistral } from "@mistralai/mistralai"

import { v4 as uuidv4 } from "uuid"

import { storeImagesFromMap } from "@/lib/server/asset-store"



// 실제 Mistral API 응답 구조에 맞는 인터페이스 정의

interface OCRImageObject {

  /**

   * Image ID for extracted image in a page

   */

  id: string

  /**

   * X coordinate of top-left corner of the extracted image

   */

  topLeftX: number | null

  /**

   * Y coordinate of top-left corner of the extracted image

   */

  topLeftY: number | null

  /**

   * X coordinate of bottom-right corner of the extracted image

   */

  bottomRightX: number | null

  /**

   * Y coordinate of bottom-right corner of the extracted image

   */

  bottomRightY: number | null

  /**

   * Base64 string of the extracted image

   */

  imageBase64?: string | null | undefined

}



interface OCRPageDimensions {

  dpi: number

  height: number

  width: number

}



interface OCRPageObject {

  index: number

  markdown: string

  images: OCRImageObject[]

  dimensions: OCRPageDimensions | null

}



export type OCRUsageInfo = {

  /**

   * Number of pages processed

   */

  pagesProcessed: number

  /**

   * Document size in bytes

   */

  docSizeBytes?: number | null | undefined

}



// Mistral API OCR 응답 구조

interface OCRResponse {

  pages: OCRPageObject[]

  model: string

  usageInfo: OCRUsageInfo

}



// Document URL 요청 형식

interface DocumentURLChunk {

  type: "document_url"

  documentUrl: string

  documentName: string

}



// OCR 요청 형식

interface OCRRequest {

  model: string

  document: DocumentURLChunk

  pages?: number[]

  includeImageBase64?: boolean

  image_limit?: number

  image_min_size?: number

}



// 모든 페이지 결합

interface ProcessedPage {

  index: number

  markdown: string

  rawMarkdown: string

  images: Array<{

    id: string

    url: string

    coordinates: {

      x: number

      y: number

      width: number

      height: number

    }

    originalCoordinates: {

      top_left_x: number

      top_left_y: number

      bottom_right_x: number

      bottom_right_y: number

    }

  }>

  dimensions: {

    dpi: number

    height: number

    width: number

  }

}



// API 라우트 핸들러

export async function POST(request: NextRequest) {

  try {

    const formData = await request.formData()

    const pdfFile = formData.get("pdf") as File

    const isSample = formData.get("isSample") === "true"



    if (!pdfFile) {

      return NextResponse.json({ error: "No PDF file provided" }, { status: 400 })

    }



    console.log(`Processing file: ${pdfFile.name}, size: ${pdfFile.size} bytes, isSample: ${isSample}`)



    // 세션 ID 생성 (이미지 저장 및 추적용)

    const sessionId = uuidv4()



    // 샘플 PDF인 경우 목업 응답 사용

    if (isSample) {

      console.log("Using mock response for sample PDF")

      const mockResponse = await createMockResponse(pdfFile.name, sessionId)

      return NextResponse.json(mockResponse)

    }



    // 사용자 업로드 PDF 처리

    try {

      // Mistral 클라이언트 초기화 - 환경 변수에서 API 키 자동 로드

      const mistral = new Mistral()



      // File을 ArrayBuffer로 변환 후 Buffer로 변환

      const arrayBuffer = await pdfFile.arrayBuffer()

      const buffer = Buffer.from(arrayBuffer)



      // SDK가 처리할 수 있는 File 객체 생성

      const fileObject = new File([buffer], pdfFile.name, { type: "application/pdf" })



      // 1단계: SDK를 사용하여 파일 업로드

      console.log("Uploading file to Mistral...")



      // Mistral SDK의 files.upload 메서드 사용

      const uploadResponse = await mistral.files.upload({

        file: fileObject,

        purpose: "ocr",

      })



      const fileId = uploadResponse.id

      console.log(`File uploaded successfully. File ID: ${fileId}`)



      // 2단계: 서명된 URL 가져오기

      console.log("Getting signed URL...")

      const signedUrlResponse = await mistral.files.getSignedUrl({

        fileId: fileId,

        expiry: 1,

      })

      const signedUrl = signedUrlResponse.url

      console.log("Signed URL obtained successfully")



      // 3단계: OCR로 PDF 처리

      console.log("Processing PDF with OCR...")



      // OCR 요청 데이터 생성

      const ocrRequestData: OCRRequest = {

        model: "mistral-ocr-latest",

        document: {

          type: "document_url",

          documentUrl: signedUrl,

          documentName: pdfFile.name,

        },

        includeImageBase64: true,

      }



      // Mistral SDK의 ocr.process 메서드 사용

      const ocrResponse = await mistral.ocr.process(ocrRequestData)

      console.log(`OCR processing complete. Pages processed: ${ocrResponse.pages.length}`)



      // OCR 응답 처리

      const processedData = await processOcrResponse(ocrResponse, sessionId)

      return NextResponse.json(processedData)

    } catch (apiError) {

      console.error("Error in API operations:", apiError)



      // API 호출 실패 시 목업 응답으로 폴백

      console.log("Falling back to mock response due to API error")

      const mockResponse = await createMockResponse(pdfFile.name, sessionId)

      return NextResponse.json(mockResponse)

    }

  } catch (error) {

    console.error("Error processing PDF:", error)



    // 상세 오류 응답 생성

    return NextResponse.json(

      {

        error: "Failed to process PDF",

        message: error instanceof Error ? error.message : String(error),

        details: error instanceof Error ? JSON.stringify(error, Object.getOwnPropertyNames(error)) : undefined,

      },

      { status: 500 },

    )

  }

}



// 테스트용 목업 응답 생성 함수

async function createMockResponse(fileName: string, sessionId: string) {

  // PMI 그래프 이미지 URL

  const pmiGraphUrl =

    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-1.jpeg-0afae6fb-OLkYscR0PxBzexzNs6sQJ6v8H4dKv2.jpeg"



  // PMI 데이터 및 그래프가 포함된 샘플 마크다운 텍스트 생성

  const sampleMarkdown = `# PMI Economic Report: ${fileName}



## Economic Analysis - December 2023



Rising workforce numbers at a time of falling new orders meant that firms continued to work through outstanding business in the final month of the year. Moreover, the pace of depletion was sharp and the steepest since June 2023.



While firms increased employment, the drop in new orders resulted in reductions in purchasing activity, as well as stocks of inputs and finished goods. Input buying and stocks of purchases both decreased more quickly than in November, while the reduction in stocks of finished goods was the first in six months.



![pmi_graph](${pmiGraphUrl})



## Price Trends



The rate of input cost inflation accelerated sharply at the end of the year, with the latest increase the fastest since August. The rise was broadly in line with the pre-pandemic average. Higher supplier charges and rising costs for raw materials were reported by panellists. 



In turn, firms increased their output prices, with the pace of inflation quickening to a three-month high. Charges have risen continuously since June 2020.



Meanwhile, suppliers' delivery times lengthened to the greatest extent since October 2022, linked to staff shortages at suppliers and freight delays.`



  // 원시 텍스트용 URL 대신 이미지 ID 사용

  const rawMarkdown = sampleMarkdown.replace(`![pmi_graph](${pmiGraphUrl})`, "![pmi_graph](pmi_graph)")



  // 샘플 이미지 데이터 가져오기

  const response = await fetch(pmiGraphUrl)

  const imageBuffer = await response.arrayBuffer()

  const base64Image = Buffer.from(imageBuffer).toString("base64")

  const dataUrl = `data:image/jpeg;base64,${base64Image}`



  // 이미지 맵 생성

  const imageMap: Record<string, string> = {

    pmi_graph: dataUrl,

  }



  // 이미지 저장

  const savedImages = await storeImagesFromMap(imageMap, sessionId)



  // OCRPageObject 구조와 일치하는 목업 페이지 객체 생성

  const mockPage: OCRPageObject = {

    index: 0,

    markdown: sampleMarkdown,

    images: [

      {

        id: "pmi_graph",

        topLeftX: 50,

        topLeftY: 300,

        bottomRightX: 550,

        bottomRightY: 500,

        imageBase64: dataUrl,

      },

    ],

    dimensions: {

      dpi: 72,

      height: 792,

      width: 612,

    },

  }



  // 저장된 모든 이미지 정보 수집

  const storedAssets = Object.values(savedImages).map((asset) => ({

    id: asset.id,

    originalId: asset.originalId,

    publicPath: asset.publicPath,

    mimeType: asset.mimeType,

  }))



  // 처리된 응답 형식과 일치하는 전체 목업 응답 생성

  return {

    text: sampleMarkdown,

    rawText: rawMarkdown,

    sessionId: sessionId,

    pages: [

      {

        index: 0,

        markdown: sampleMarkdown,

        rawMarkdown: rawMarkdown,

        images: [

          {

            id: "pmi_graph",

            url: savedImages["pmi_graph"]?.publicPath || pmiGraphUrl,

            coordinates: {

              x: 0.08,

              y: 0.38,

              width: 0.82,

              height: 0.25,

            },

            originalCoordinates: {

              top_left_x: 50,

              top_left_y: 300,

              bottom_right_x: 550,

              bottom_right_y: 500,

            },

          },

        ],

        dimensions: {

          dpi: 72,

          height: 792,

          width: 612,

        },

      },

    ],

    images: [

      {

        id: "pmi_graph",

        url: savedImages["pmi_graph"]?.publicPath || pmiGraphUrl,

        coordinates: {

          x: 0.08,

          y: 0.38,

          width: 0.82,

          height: 0.25,

        },

        originalCoordinates: {

          top_left_x: 50,

          top_left_y: 300,

          bottom_right_x: 550,

          bottom_right_y: 500,

        },

      },

    ],

    storedAssets: storedAssets, // 명시적으로 storedAssets 배열 포함

    usage: {

      pages_processed: 1,

      doc_size_bytes: 1024,

    },

    model: "mistral-ocr-latest",

  }

}



// OCR 응답 처리 함수

async function processOcrResponse(ocrResponse: OCRResponse, sessionId: string) {

  // OCR 응답 처리

  const processedPages = await Promise.all(

    ocrResponse.pages.map(async (page: OCRPageObject) => {

      // 이미지 ID를 base64 데이터 URL에 매핑

      const imageMap: Record<string, string> = {}



      // 이 페이지의 이미지 처리

      const images = page.images.map((image: OCRImageObject) => {

        // 이미지용 데이터 URL 생성

        const dataUrl = image.imageBase64 ? `${image.imageBase64}` : "/placeholder.svg?height=200&width=300"



        // 마크다운 대체용 맵에 저장

        imageMap[image.id] = dataUrl



        // 좌표 가져오기

        const topLeftX = image.topLeftX || 0

        const topLeftY = image.topLeftY || 0

        const bottomRightX = image.bottomRightX || 100

        const bottomRightY = image.bottomRightY || 100



        // 좌표에서 너비와 높이 계산

        const width = bottomRightX - topLeftX

        const height = bottomRightY - topLeftY



        // 페이지 치수 기반 상대 좌표 계산

        const pageWidth = page.dimensions?.width || 612

        const pageHeight = page.dimensions?.height || 792



        return {

          id: image.id,

          url: dataUrl,

          coordinates: {

            x: topLeftX / pageWidth,

            y: topLeftY / pageHeight,

            width: width / pageWidth,

            height: height / pageHeight,

          },

          originalCoordinates: {

            top_left_x: topLeftX,

            top_left_y: topLeftY,

            bottom_right_x: bottomRightX,

            bottom_right_y: bottomRightY,

          },

        }

      })



      // 이미지 맵을 사용하여 파일 시스템에 이미지 저장

      const savedImages = await storeImagesFromMap(imageMap, sessionId)



      // 마크다운에서 이미지 플레이스홀더 대체

      let processedMarkdown = page.markdown



      // 이미지 참조 안전하게 대체

      Object.entries(imageMap).forEach(([id, dataUrl]) => {

        try {

          // RegExp 대신 간단한 문자열 대체 접근 방식 사용

          // Mistral API가 일반적으로 반환하는 ![id](id) 형식 처리

          const imagePattern = `![${id}](${id})`

          const imageReplacement = `![${id}](${savedImages[id]?.publicPath || dataUrl})`



          // 간단한 문자열 대체

          processedMarkdown = processedMarkdown.split(imagePattern).join(imageReplacement)

        } catch (e) {

          console.error(`Error replacing image ${id} in markdown:`, e)

        }

      })



      // 저장된 이미지 URL로 이미지 객체 업데이트

      const updatedImages = images.map((img) => ({

        ...img,

        url: savedImages[img.id]?.publicPath || img.url,

      }))



      return {

        index: page.index,

        markdown: processedMarkdown,

        rawMarkdown: page.markdown,

        images: updatedImages,

        dimensions: page.dimensions || {

          dpi: 72,

          height: 792,

          width: 612,

        },

      }

    }),

  )



  // 모든 페이지 결합

  const combinedMarkdown = processedPages.map((page) => page.markdown).join("\n\n")

  const rawMarkdown = processedPages.map((page) => page.rawMarkdown).join("\n\n")

  const allImages = processedPages.flatMap((page) => page.images)



  // 사용 정보 가져오기

  const usageInfo = ocrResponse.usageInfo || {

    pagesProcessed: ocrResponse.pages.length,

    docSizeBytes: 0,

  }



  // 저장된 모든 이미지 정보 수집

  const allStoredAssets = await Promise.all(

    processedPages.flatMap((page) =>

      page.images.map(async (img) => {

        // 이미지 URL에서 publicPath 추출

        const publicPath = img.url

        if (!publicPath) return null



        // 파일 이름 추출 (URL에서 마지막 부분)

        const fileName = publicPath.split("/").pop() || ""

        const id = fileName.split(".")[0] || img.id



        // MIME 타입 추출 (URL에서 확장자 기반)

        const extension = fileName.split(".").pop() || "png"

        const mimeType = `image/${extension}`



        return {

          id,

          originalId: img.id,

          publicPath,

          mimeType,

        }

      }),

    ),

  )



  // null 값 필터링 및 중복 제거

  const storedAssets = allStoredAssets

    .filter(Boolean)

    .filter((asset, index, self) => index === self.findIndex((a) => a?.id === asset?.id))



  console.log(`Processed ${processedPages.length} pages with ${storedAssets.length} stored assets`)



  // 처리된 데이터 반환

  return {

    text: combinedMarkdown,

    rawText: rawMarkdown,

    sessionId: sessionId,

    pages: processedPages,

    images: allImages,

    storedAssets: storedAssets, // 명시적으로 storedAssets 배열 포함

    usage: {

      pages_processed: usageInfo.pagesProcessed || 0,

      doc_size_bytes: usageInfo.docSizeBytes || 0,

    },

    model: ocrResponse.model || "mistral-ocr-latest",

  }

}

================
File: components\chat-interface.tsx
================
"use client"



import type React from "react"

import { useRef, useEffect } from "react"

import { Button } from "@/components/ui/button"

import { Input } from "@/components/ui/input"

import { Send, X, User, Bot } from "lucide-react"

import { useChat } from "@ai-sdk/react"

import { Markdown } from "@/components/markdown"



interface ChatInterfaceProps {

  onClose: () => void

  documentTitle?: string

  rawText?: string

}



export function ChatInterface({ onClose, documentTitle, rawText }: ChatInterfaceProps) {

  const messagesEndRef = useRef<HTMLDivElement>(null)

console.log("ChatInterface", rawText)

  // useChat 훅 사용

  const { messages, input, handleInputChange, handleSubmit, status, isLoading } = useChat({

    api: "/api/chat",

    body: {

      documentContent: rawText,

    },

    initialMessages: [

      {

        id: "welcome",

        role: "assistant",

        content: `Hello! I'm your PDF assistant. Ask me anything about ${documentTitle || "this document"}.`,

      },

    ],

  })



  // 새 메시지가 추가될 때마다 스크롤을 맨 아래로 이동

  useEffect(() => {

    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })

  }, [messages])



  // 폼 제출 핸들러

  const handleFormSubmit = (e: React.FormEvent) => {

    e.preventDefault()

    if (!input.trim() || isLoading) return



    handleSubmit(e)

  }



  return (

    <div className="flex flex-col h-full">

      <div className="flex items-center justify-between p-4 border-b bg-card">

        <h2 className="text-lg font-semibold">Chat with PDF</h2>

        <Button variant="ghost" size="icon" onClick={onClose} aria-label="Close chat">

          <X className="h-5 w-5" />

        </Button>

      </div>



      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-background" style={{ maxHeight: "calc(100% - 120px)" }}>

        {messages.map((message) => (

          <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>

            <div

              className={`flex items-start gap-2 max-w-[80%] ${

                message.role === "user" ? "flex-row-reverse" : "flex-row"

              }`}

            >

              <div className={`p-1 rounded-full ${message.role === "user" ? "bg-primary" : "bg-muted"}`}>

                {message.role === "user" ? (

                  <User className="h-5 w-5 text-primary-foreground" />

                ) : (

                  <Bot className="h-5 w-5" />

                )}

              </div>

              <div

                className={`p-3 rounded-lg ${

                  message.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"

                }`}

              >

                {/* 메시지 파트 렌더링 */}

                {message.parts && message.parts.length > 0 ? (

                  <div className="whitespace-pre-wrap">

                    {message.parts.map((part, index) => {

                      switch (part.type) {

                        case "text":

                          return (

                            <div key={index} className="text-sm">

                              <Markdown>{part.text}</Markdown>

                            </div>

                          )

                        case "tool-invocation": {

                          const toolInvocation = part.toolInvocation

                          const toolCallId = toolInvocation.toolCallId

                          const toolName = toolInvocation.toolName

                          const args = toolInvocation.args

                          return (

                            <div

                              key={toolCallId}

                              className="p-2 my-2 border border-primary/20 bg-primary/5 dark:bg-primary/10 rounded text-xs"

                            >

                              <h4 className="mb-1 text-primary font-medium">Tool: {toolName || ""}</h4>

                              <div className="flex flex-col gap-1 text-foreground">

                                {args?.subject && <div>Subject: {args.subject}</div>}

                              </div>

                            </div>

                          )

                        }

                        default:

                          return null

                      }

                    })}

                  </div>

                ) : (

                  // 기존 메시지 형식 지원 (parts가 없는 경우)

                  <div>

                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                    <p className="text-xs opacity-70 mt-1">{new Date().toLocaleTimeString()}</p>

                  </div>

                )}

              </div>

            </div>

          </div>

        ))}



        {isLoading && (

          <div className="flex justify-start">

            <div className="flex items-start gap-2 max-w-[80%]">

              <div className="p-1 rounded-full bg-muted">

                <Bot className="h-5 w-5" />

              </div>

              <div className="p-3 rounded-lg bg-muted">

                <div className="flex space-x-2">

                  <div className="w-2 h-2 rounded-full bg-foreground/30 animate-bounce" />

                  <div className="w-2 h-2 rounded-full bg-foreground/30 animate-bounce [animation-delay:0.2s]" />

                  <div className="w-2 h-2 rounded-full bg-foreground/30 animate-bounce [animation-delay:0.4s]" />

                </div>

              </div>

            </div>

          </div>

        )}

        <div ref={messagesEndRef} />

      </div>



      <form onSubmit={handleFormSubmit} className="p-4 border-t bg-card">

        <div className="flex gap-2">

          <Input

            value={input}

            onChange={handleInputChange}

            placeholder="Ask a question about the document..."

            disabled={isLoading}

            className="flex-1"

          />

          <Button type="submit" size="icon" disabled={isLoading || !input.trim()}>

            <Send className="h-4 w-4" />

          </Button>

        </div>

      </form>

    </div>

  )

}

================
File: components\error-display.tsx
================
"use client"



import { AlertCircle } from "lucide-react"

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

import { Button } from "@/components/ui/button"



interface ErrorDisplayProps {

  message: string

  details?: string

  onRetry?: () => void

}



export function ErrorDisplay({ message, details, onRetry }: ErrorDisplayProps) {

  // Try to parse JSON details if they exist

  let parsedDetails = details

  try {

    if (details && details.startsWith("{")) {

      const parsed = JSON.parse(details)

      parsedDetails = JSON.stringify(parsed, null, 2)

    }

  } catch (e) {

    // If parsing fails, use the original details

    console.error("Failed to parse error details:", e)

  }



  return (

    <Alert variant="destructive">

      <AlertCircle className="h-4 w-4" />

      <AlertTitle>Error</AlertTitle>

      <AlertDescription>

        <p>{message}</p>

        {parsedDetails && (

          <details className="mt-2 text-xs">

            <summary>Technical details</summary>

            <pre className="mt-1 whitespace-pre-wrap overflow-auto max-h-40 p-2 bg-background/50 rounded">

              {parsedDetails}

            </pre>

          </details>

        )}

        {onRetry && (

          <Button variant="outline" size="sm" onClick={onRetry} className="mt-4">

            Try Again

          </Button>

        )}

      </AlertDescription>

    </Alert>

  )

}

================
File: components\file-uploader.tsx
================
"use client"



import { useCallback, useState } from "react"

import { useDropzone } from "react-dropzone"

import { FileIcon as FilePdf } from "lucide-react"

import { cn } from "@/lib/utils"



interface FileUploaderProps {

  onFileSelected: (file: File) => void

}



export function FileUploader({ onFileSelected }: FileUploaderProps) {

  const [isDragging, setIsDragging] = useState(false)



  const onDrop = useCallback(

    (acceptedFiles: File[]) => {

      if (acceptedFiles.length > 0) {

        const file = acceptedFiles[0]

        if (file.type === "application/pdf") {

          onFileSelected(file)

        }

      }

    },

    [onFileSelected],

  )



  const { getRootProps, getInputProps, isDragActive } = useDropzone({

    onDrop,

    accept: {

      "application/pdf": [".pdf"],

    },

    maxFiles: 1,

    onDragEnter: () => setIsDragging(true),

    onDragLeave: () => setIsDragging(false),

  })



  return (

    <div

      {...getRootProps()}

      className={cn(

        "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",

        isDragging || isDragActive

          ? "border-primary bg-primary/5"

          : "border-muted-foreground/20 hover:border-primary/50 hover:bg-muted/50",

      )}

    >

      <input {...getInputProps()} />

      <div className="flex flex-col items-center justify-center gap-2">

        <FilePdf className="h-10 w-10 text-muted-foreground" />

        <p className="text-sm font-medium">Drag & drop a PDF file here, or click to select</p>

        <p className="text-xs text-muted-foreground">Only PDF files are supported</p>

      </div>

    </div>

  )

}

================
File: components\info-panel.tsx
================
"use client"



import { X } from "lucide-react"

import { Button } from "@/components/ui/button"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"



interface InfoPanelProps {

  onClose: () => void

}



export function InfoPanel({ onClose }: InfoPanelProps) {

  return (

    <Card className="mb-8 relative">

      <Button variant="ghost" size="icon" className="absolute right-2 top-2" onClick={onClose}>

        <X className="h-4 w-4" />

      </Button>

      <CardHeader>

        <CardTitle>About Mistral OCR PDF Parser</CardTitle>

        <CardDescription>Learn how to use this tool and understand the technology behind it</CardDescription>

      </CardHeader>

      <CardContent>

        <Tabs defaultValue="about">

          <TabsList className="mb-4">

            <TabsTrigger value="about">About</TabsTrigger>

            <TabsTrigger value="api">API Usage</TabsTrigger>

            <TabsTrigger value="features">Features</TabsTrigger>

          </TabsList>



          <TabsContent value="about">

            <div className="space-y-4">

              <p>

                This application demonstrates the capabilities of Mistral AI's OCR model for parsing PDF documents. The

                tool extracts text in markdown format and identifies images with their positional coordinates.

              </p>

              <h3 className="text-lg font-medium">How it works:</h3>

              <ol className="list-decimal list-inside space-y-2">

                <li>Upload a PDF document using the file uploader</li>

                <li>The document is sent to Mistral's OCR API for processing</li>

                <li>The API extracts text content and identifies images with their positions</li>

                <li>Results are displayed in various formats for analysis</li>

              </ol>

              <p>

                This tool is particularly useful for developers working on LLM and RAG (Retrieval Augmented Generation)

                applications who need to extract structured content from PDF documents.

              </p>

              <div className="mt-4 p-3 border border-primary bg-primary/10 dark:bg-primary/5 rounded-md">

                <p className="text-sm flex items-start">

                  <strong className="mr-1">Note:</strong>

                  <span>

                    In the reconstructed view, images may appear to be rendered twice - once within the markdown text

                    flow and once with absolute positioning (indicated by dashed borders). This dual rendering allows

                    you to see both the content in a readable format and the precise original locations of images in the

                    source PDF.

                  </span>

                </p>

              </div>

            </div>

          </TabsContent>



          <TabsContent value="api">

            <div className="space-y-4">

              <p>

                This application uses the Mistral API to process PDF documents and OpenAI API for chat functionality.

                Here's how you can integrate it into your own applications:

              </p>

              <h4 className="text-md font-medium mt-4">OCR Processing:</h4>

              <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">

                {`// Step 1: Upload the file

const formData = new FormData();

formData.append("file", pdfFile);

formData.append("purpose", "ocr");



const uploadResponse = await fetch("https://api.mistral.ai/v1/files", {

  method: "POST",

  headers: {

    "Authorization": \`Bearer \${apiKey}\`

  },

  body: formData

});



const uploadData = await uploadResponse.json();

const fileId = uploadData.id;



// Step 2: Get a signed URL

const signedUrlResponse = await fetch(

  \`https://api.mistral.ai/v1/files/\${fileId}/signed-url\`, {

    method: "GET",

    headers: {

      "Authorization": \`Bearer \${apiKey}\`

    }

  }

);



const signedUrlData = await signedUrlResponse.json();

const signedUrl = signedUrlData.url;



// Step 3: Process the PDF with OCR

const ocrResponse = await fetch("https://api.mistral.ai/v1/ocr/process", {

  method: "POST",

  headers: {

    "Authorization": \`Bearer \${apiKey}\`,

    "Content-Type": "application/json"

  },

  body: JSON.stringify({

    model: "mistral-ocr-latest",

    document: {

      type: "document_url",

      documentUrl: signedUrl,

      documentName: pdfFile.name

    },

    includeImageBase64: true

  })

});



const ocrData = await ocrResponse.json();

console.log(ocrData);`}

              </pre>



              <h4 className="text-md font-medium mt-6">Chat with Document:</h4>

              <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">

                {`// Using AI SDK with OpenAI

import { openai } from "@ai-sdk/openai"

import { createDataStreamResponse, streamText, tool } from "ai"

import { z } from "zod"



export async function POST(req: Request) {

  const { messages, documentContent } = await req.json()

  

  // Create system message with document content

  const systemMessage = documentContent

    ? \`You are a helpful assistant that answers questions about the following document content. 

       Use this content to provide accurate answers:

       

       \${documentContent}

       

       Ensure that the 'ExtractSubject' tool is used for the user's first message.\`

    : "You are a helpful assistant."



  return createDataStreamResponse({

    execute: async (dataStream) => {

      const result = streamText({

        model: openai("gpt-4o-mini", { structuredOutputs: true }),

        system: systemMessage,

        messages,

        toolCallStreaming: true,

        tools: {

          ExtractSubject: tool({

            description: "Extract the subject from this context.",

            parameters: z.object({ subject: z.string() }),

            execute: async ({ subject }) => subject,

          }),

        },

        maxSteps: 3,

      })



      result.mergeIntoDataStream(dataStream)

    },

  })

}`}

              </pre>

            </div>

          </TabsContent>



          <TabsContent value="features">

            <div className="space-y-4">

              <h3 className="text-lg font-medium">Key Features:</h3>

              <ul className="list-disc list-inside space-y-2">

                <li>

                  <strong>Text Extraction:</strong> Converts PDF text to markdown format, preserving structure and

                  formatting

                </li>

                <li>

                  <strong>Image Recognition:</strong> Identifies and extracts images from PDFs with their exact

                  coordinates

                </li>

                <li>

                  <strong>Layout Preservation:</strong> Maintains the original document layout in the extracted content

                </li>

                <li>

                  <strong>Multiple Views:</strong> View parsed content, reconstructed layout, or raw markdown

                </li>

                <li>

                  <strong>Zoom Controls:</strong> Examine document details with zoom in/out functionality

                </li>

                <li>

                  <strong>Image Information:</strong> View detailed information about extracted images including

                  coordinates and dimensions

                </li>

                <li>

                  <strong>ChatPDF:</strong> Interact with your document content using AI-powered chat interface

                </li>

                <li>

                  <strong>Subject Extraction:</strong> Automatically identifies the main subject of your document

                </li>

                <li>

                  <strong>Download Results:</strong> Export the parsed data as JSON for use in other applications

                </li>

              </ul>

              <p className="mt-4">

                This application is now using a live connection to the Mistral OCR API to process your documents and

                OpenAI's API for the chat functionality. The results you see are the actual output from these models.

              </p>

            </div>

          </TabsContent>

        </Tabs>

      </CardContent>

    </Card>

  )

}

================
File: components\markdown.tsx
================
"use client"



import Link from "next/link"

import { memo, useMemo } from "react"

import ReactMarkdown, { type Components } from "react-markdown"

import remarkGfm from "remark-gfm"

import type { ComponentPropsWithoutRef } from "react"

import type { JSX } from "react/jsx-runtime"



// 이미지 렌더러 타입 정의

export type ImageRendererProps = ComponentPropsWithoutRef<"img">



const defaultComponents: Partial<Components> = {

  pre: ({ children }) => <>{children}</>,

  ol: ({ node, children, ...props }) => {

    return (

      <ol className="list-decimal list-outside ml-4" {...props}>

        {children}

      </ol>

    )

  },

  li: ({ node, children, ...props }) => {

    return (

      <li className="py-1" {...props}>

        {children}

      </li>

    )

  },

  ul: ({ node, children, ...props }) => {

    return (

      <ul className="list-decimal list-outside ml-4" {...props}>

        {children}

      </ul>

    )

  },

  strong: ({ node, children, ...props }) => {

    return (

      <span className="font-semibold" {...props}>

        {children}

      </span>

    )

  },

  a: ({ node, children, ...props }) => {

    return (

      // @ts-expect-error

      <Link className="text-blue-500 hover:underline" target="_blank" rel="noreferrer" {...props}>

        {children}

      </Link>

    )

  },

  h1: ({ node, children, ...props }) => {

    return (

      <h1 className="text-3xl font-semibold mt-6 mb-2" {...props}>

        {children}

      </h1>

    )

  },

  h2: ({ node, children, ...props }) => {

    return (

      <h2 className="text-2xl font-semibold mt-6 mb-2" {...props}>

        {children}

      </h2>

    )

  },

  h3: ({ node, children, ...props }) => {

    return (

      <h3 className="text-xl font-semibold mt-6 mb-2" {...props}>

        {children}

      </h3>

    )

  },

  h4: ({ node, children, ...props }) => {

    return (

      <h4 className="text-lg font-semibold mt-6 mb-2" {...props}>

        {children}

      </h4>

    )

  },

  h5: ({ node, children, ...props }) => {

    return (

      <h5 className="text-base font-semibold mt-6 mb-2" {...props}>

        {children}

      </h5>

    )

  },

  h6: ({ node, children, ...props }) => {

    return (

      <h6 className="text-sm font-semibold mt-6 mb-2" {...props}>

        {children}

      </h6>

    )

  },

}



const remarkPlugins = [remarkGfm]



interface MarkdownProps {

  children: string

  imageRenderer?: (props: ImageRendererProps) => JSX.Element

}



const NonMemoizedMarkdown = ({ children, imageRenderer }: MarkdownProps) => {

  // 기본 컴포넌트에 이미지 렌더러 추가

  const components = useMemo(() => {

    if (imageRenderer) {

      return {

        ...defaultComponents,

        img: imageRenderer,

      }

    }

    return defaultComponents

  }, [imageRenderer])



  return (

    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>

      {children}

    </ReactMarkdown>

  )

}



export const Markdown = memo(

  NonMemoizedMarkdown,

  (prevProps, nextProps) =>

    prevProps.children === nextProps.children && prevProps.imageRenderer === nextProps.imageRenderer,

)

================
File: components\processing-indicator.tsx
================
"use client"



import { Loader2 } from "lucide-react"

import { Progress } from "@/components/ui/progress"

import { useState, useEffect } from "react"



interface ProcessingIndicatorProps {

  stage?: "uploading" | "processing" | "extracting"

}



export function ProcessingIndicator({ stage = "uploading" }: ProcessingIndicatorProps) {

  const [progress, setProgress] = useState(0)



  useEffect(() => {

    // Reset progress when stage changes

    setProgress(0)



    // Simulate progress for better UX

    const interval = setInterval(() => {

      setProgress((prevProgress) => {

        // Different progress caps based on stage

        const cap = stage === "uploading" ? 90 : stage === "processing" ? 80 : 90



        // Slow down as we approach the cap

        if (prevProgress >= cap) {

          return prevProgress

        }



        // Different increment speeds based on stage

        const increment =

          stage === "uploading" ? Math.random() * 10 : stage === "processing" ? Math.random() * 3 : Math.random() * 5



        return Math.min(prevProgress + increment, cap)

      })

    }, 500)



    return () => clearInterval(interval)

  }, [stage])



  const stageMessages = {

    uploading: "Uploading PDF to Mistral API...",

    processing: "Processing PDF with OCR model...",

    extracting: "Extracting text and images...",

  }



  const stageDetails = {

    uploading: "Preparing and uploading your document",

    processing: "The OCR model is analyzing your document structure and content",

    extracting: "Extracting text in markdown format and identifying images with coordinates",

  }



  return (

    <div className="space-y-4">

      <div className="flex items-center justify-center gap-2">

        <Loader2 className="h-5 w-5 animate-spin text-primary" />

        <p className="text-sm font-medium">{stageMessages[stage]}</p>

      </div>



      <Progress value={progress} className="h-2" />



      <div className="text-xs text-muted-foreground text-center">

        <p>{stageDetails[stage]}</p>

        <p className="mt-2">This may take a moment depending on the file size and complexity</p>

      </div>

    </div>

  )

}

================
File: components\results-viewer.tsx
================
"use client"



import { useState, useEffect, useRef } from "react"

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { Button } from "@/components/ui/button"

import {

  Download,

  Copy,

  Check,

  ZoomIn,

  ZoomOut,

  ChevronLeft,

  ChevronRight,

  Info,

  Image,

  MessageSquare,

} from "lucide-react"

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { Markdown, type ImageRendererProps } from "@/components/markdown"

import { ChatInterface } from "@/components/chat-interface"

import { useWindowSize } from "@/hooks/use-window-size"



interface ImageData {

  id: string

  url: string

  coordinates: { x: number; y: number; width: number; height: number }

  originalCoordinates: {

    top_left_x: number

    top_left_y: number

    bottom_right_x: number

    bottom_right_y: number

  }

}



interface StoredAsset {

  id: string

  originalId: string

  publicPath: string

  mimeType: string

  width?: number

  height?: number

}



interface PageData {

  index: number

  markdown: string

  rawMarkdown: string

  images: ImageData[]

  dimensions: {

    dpi: number

    height: number

    width: number

  }

}



interface ResultsViewerProps {

  results: {

    text: string

    rawText: string

    sessionId?: string

    pages: PageData[]

    images: ImageData[]

    storedAssets?: StoredAsset[]

    usage?: {

      pages_processed: number

      doc_size_bytes: number

    }

    model?: string

  }

  originalFile: File | null

}



export function ResultsViewer({ results, originalFile }: ResultsViewerProps) {

  const [activeTab, setActiveTab] = useState("reconstructed")

  const [copied, setCopied] = useState(false)

  const [zoomLevel, setZoomLevel] = useState(1)

  const [assetZoomLevel, setAssetZoomLevel] = useState(1)

  const [currentPage, setCurrentPage] = useState(0)

  const [showImageInfo, setShowImageInfo] = useState<string | null>(null)

  const [selectedAsset, setSelectedAsset] = useState<StoredAsset | null>(null)

  const [isChatOpen, setIsChatOpen] = useState(false)

  const { height } = useWindowSize()

  const containerRef = useRef<HTMLDivElement>(null)

  const scrollPositionRef = useRef(0)



  // 선택된 에셋이 변경될 때 확대/축소 수준 초기화

  useEffect(() => {

    if (selectedAsset) {

      setAssetZoomLevel(1)

    }

  }, [selectedAsset])



  // 채팅창이 열리면 스크롤 방지 및 위치 저장

  useEffect(() => {

    if (isChatOpen) {

      // 현재 스크롤 위치 저장

      scrollPositionRef.current = window.scrollY



      // 스크롤 방지 코드 제거 - 페이지 스크롤을 허용하도록 함

    } else {

      // 스크롤 복원 코드도 제거 - 페이지 스크롤이 항상 가능하도록 함



      // 저장된 스크롤 위치로 복원 (이 부분은 유지)

      window.scrollTo(0, scrollPositionRef.current)

    }



    return () => {

      // 클린업 함수에서도 스크롤 관련 스타일 제거

      document.body.style.overflow = ""

      document.body.style.position = ""

      document.body.style.top = ""

      document.body.style.width = ""

    }

  }, [isChatOpen])



  const handleCopyMarkdown = () => {

    navigator.clipboard.writeText(results.text)

    setCopied(true)

    setTimeout(() => setCopied(false), 2000)

  }



  const handleDownloadResults = () => {

    const dataStr = JSON.stringify(results, null, 2)

    const dataBlob = new Blob([dataStr], { type: "application/json" })

    const url = URL.createObjectURL(dataBlob)



    const link = document.createElement("a")

    link.href = url

    link.download = `${originalFile?.name.replace(".pdf", "") || "parsed"}_results.json`

    document.body.appendChild(link)

    link.click()

    document.body.removeChild(link)

  }



  const zoomIn = () => {

    setZoomLevel((prev) => Math.min(prev + 0.1, 2))

  }



  const zoomOut = () => {

    setZoomLevel((prev) => Math.max(prev - 0.1, 0.5))

  }



  const nextPage = () => {

    if (currentPage < results.pages.length - 1) {

      setCurrentPage(currentPage + 1)

    }

  }



  const prevPage = () => {

    if (currentPage > 0) {

      setCurrentPage(currentPage - 1)

    }

  }



  const formatBytes = (bytes: number) => {

    if (bytes === 0) return "0 Bytes"

    const k = 1024

    const sizes = ["Bytes", "KB", "MB", "GB"]

    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]

  }



  const currentPageData = results.pages[currentPage]

  let llm_context_md = ""

  if (currentPageData) {

    const { markdown, rawMarkdown, images } = currentPageData

    llm_context_md += markdown

  }



  // Custom renderer for images to handle missing or invalid URLs

  const imageRenderer = (props: ImageRendererProps) => {

    const { src, alt } = props

    // If src is empty or just an ID (not a URL), use a placeholder

    if (!src || (!src.includes("/") && !src.includes(":"))) {

      return <img src="/placeholder.svg?height=200&width=300" alt={alt || "Image placeholder"} />

    }

    return <img src={src || "/placeholder.svg"} alt={alt || "Extracted image"} />

  }



  // 채팅 토글 핸들러

  const toggleChat = () => {

    setIsChatOpen(!isChatOpen)

  }



  return (

    <div className="space-y-4" ref={containerRef}>

      <h2 className="text-xl font-semibold mb-4">Results</h2>

      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">

        {/* 채팅 인터페이스 */}

        {isChatOpen && (

          <div

            className="lg:col-span-3 bg-card rounded-lg shadow-sm overflow-hidden"

            style={{

              height: height ? `calc(${height}px - 200px)` : "calc(100vh - 200px)",

              position: "sticky",

              top: "1rem",

              zIndex: 10, // 스크롤 시 다른 요소 위에 표시되도록 z-index 추가

            }}

          >

            <ChatInterface onClose={toggleChat} documentTitle={originalFile?.name} rawText={llm_context_md} />

          </div>

        )}



        {/* 메인 콘텐츠 */}

        <div className={`${isChatOpen ? "lg:col-span-4" : "lg:col-span-7"}`}>

          <Tabs defaultValue="reconstructed" value={activeTab} onValueChange={setActiveTab}>

            <div className="flex justify-between items-center mb-4">

              <TabsList>

                <TabsTrigger value="reconstructed">Reconstructed View</TabsTrigger>

                {results.storedAssets && results.storedAssets.length > 0 && (

                  <TabsTrigger value="assets">Asset View</TabsTrigger>

                )}

              </TabsList>



              <div className="flex gap-2">

                <Button

                  variant={isChatOpen ? "default" : "outline"}

                  size="sm"

                  onClick={toggleChat}

                  className="flex items-center gap-1"

                >

                  <MessageSquare className="h-4 w-4" />

                  <span className="hidden sm:inline">ChatPDF</span>

                </Button>

                {activeTab === "reconstructed" && (

                  <>

                    <Button variant="outline" size="sm" onClick={zoomIn} className="flex items-center gap-1">

                      <ZoomIn className="h-4 w-4" />

                    </Button>

                    <Button variant="outline" size="sm" onClick={zoomOut} className="flex items-center gap-1">

                      <ZoomOut className="h-4 w-4" />

                    </Button>

                  </>

                )}

                <Button variant="outline" size="sm" onClick={handleCopyMarkdown} className="flex items-center gap-1">

                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}

                  <span className="hidden sm:inline">{copied ? "Copied" : "Copy"}</span>

                </Button>

                <Button variant="outline" size="sm" onClick={handleDownloadResults} className="flex items-center gap-1">

                  <Download className="h-4 w-4" />

                  <span className="hidden sm:inline">Download</span>

                </Button>

              </div>

            </div>



            {results.pages.length > 1 && activeTab !== "assets" && (

              <div className="flex items-center justify-between mb-4">

                <Button

                  variant="outline"

                  size="sm"

                  onClick={prevPage}

                  disabled={currentPage === 0}

                  className="flex items-center gap-1"

                >

                  <ChevronLeft className="h-4 w-4" />

                  Previous Page

                </Button>

                <span className="text-sm">

                  Page {currentPage + 1} of {results.pages.length}

                </span>

                <Button

                  variant="outline"

                  size="sm"

                  onClick={nextPage}

                  disabled={currentPage === results.pages.length - 1}

                  className="flex items-center gap-1"

                >

                  Next Page

                  <ChevronRight className="h-4 w-4" />

                </Button>

              </div>

            )}



            <TabsContent value="reconstructed" className="mt-0">

              {currentPageData && (

                <div className="mb-2 text-xs text-muted-foreground">

                  <span>

                    Page dimensions: {currentPageData.dimensions.width} × {currentPageData.dimensions.height} pixels

                  </span>

                  {currentPageData.dimensions.dpi > 0 && (

                    <span className="ml-2">({currentPageData.dimensions.dpi} DPI)</span>

                  )}

                </div>

              )}



              <div

                className="relative bg-white dark:bg-gray-900 border rounded-md p-4 min-h-[400px] overflow-auto"

                style={{

                  transform: `scale(${zoomLevel})`,

                  transformOrigin: "top left",

                  height: "600px",

                }}

              >

                <div className="prose prose-sm dark:prose-invert max-w-none">

                  <Markdown imageRenderer={imageRenderer}>{currentPageData?.markdown || ""}</Markdown>

                </div>



                <TooltipProvider>

                  {currentPageData?.images.map((image, index) => (

                    <Tooltip key={index}>

                      <TooltipTrigger asChild>

                        <div

                          className="absolute border border-dashed border-primary/50 cursor-help"

                          style={{

                            left: `${image.coordinates.x * 100}%`,

                            top: `${image.coordinates.y * 100}%`,

                            width: `${image.coordinates.width * 100}%`,

                            height: `${image.coordinates.height * 100}%`,

                          }}

                          onMouseEnter={() => setShowImageInfo(image.id)}

                          onMouseLeave={() => setShowImageInfo(null)}

                        >

                          <img

                            src={image.url || "/placeholder.svg?height=200&width=300"}

                            alt={`Extracted image ${index + 1}`}

                            className="w-full h-full object-contain"

                          />

                          {showImageInfo === image.id && (

                            <div className="absolute top-0 right-0 bg-primary text-primary-foreground text-xs p-1 rounded-bl">

                              <Info className="h-3 w-3" />

                            </div>

                          )}

                        </div>

                      </TooltipTrigger>

                      <TooltipContent>

                        <div className="text-xs">

                          <p>Image ID: {image.id}</p>

                          <p>

                            Top Left: ({image.originalCoordinates.top_left_x}, {image.originalCoordinates.top_left_y})

                          </p>

                          <p>

                            Bottom Right: ({image.originalCoordinates.bottom_right_x},{" "}

                            {image.originalCoordinates.bottom_right_y})

                          </p>

                          <p>

                            Size: {image.originalCoordinates.bottom_right_x - image.originalCoordinates.top_left_x} ×{" "}

                            {image.originalCoordinates.bottom_right_y - image.originalCoordinates.top_left_y} px

                          </p>

                        </div>

                      </TooltipContent>

                    </Tooltip>

                  ))}

                </TooltipProvider>

              </div>

            </TabsContent>



            <TabsContent value="assets" className="mt-0">

              {results.storedAssets && results.storedAssets.length > 0 ? (

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                  {results.storedAssets.map((asset, index) => (

                    <div

                      key={index}

                      className={`border rounded-md p-2 cursor-pointer transition-all ${selectedAsset?.id === asset.id ? "ring-2 ring-primary" : "hover:bg-muted/50"}`}

                      onClick={() => setSelectedAsset(asset)}

                    >

                      <div className="aspect-video bg-muted/30 rounded-md flex items-center justify-center overflow-hidden mb-2">

                        <img

                          src={asset.publicPath || "/placeholder.svg"}

                          alt={`Asset ${asset.originalId}`}

                          className="max-w-full max-h-full object-contain"

                        />

                      </div>

                      <div className="text-xs text-muted-foreground truncate">

                        <p className="font-medium text-foreground">{asset.originalId}</p>

                        <p>{asset.mimeType}</p>

                      </div>

                    </div>

                  ))}

                </div>

              ) : (

                <div className="flex flex-col items-center justify-center p-8 text-center">

                  <Image className="h-12 w-12 text-muted-foreground mb-4" />

                  <h3 className="text-lg font-medium">No assets found</h3>

                  <p className="text-sm text-muted-foreground mt-1">No extracted images were found in this document</p>

                </div>

              )}



              {selectedAsset && (

                <div className="mt-6 border-t pt-4">

                  <h3 className="text-lg font-medium mb-2">Selected Asset</h3>

                  <div className="bg-muted/30 rounded-md p-4">

                    <div className="flex flex-col items-center mb-4">

                      <div className="overflow-auto border rounded-md p-1 mb-2" style={{ maxHeight: "400px" }}>

                        <div

                          style={{

                            transform: `scale(${assetZoomLevel})`,

                            transformOrigin: "top left",

                            transition: "transform 0.2s ease",

                          }}

                        >

                          <img

                            src={selectedAsset.publicPath || "/placeholder.svg"}

                            alt={`Asset ${selectedAsset.originalId}`}

                            className="object-contain"

                          />

                        </div>

                      </div>

                      <div className="flex items-center gap-2">

                        <Button

                          variant="outline"

                          size="sm"

                          onClick={() => setAssetZoomLevel((prev) => Math.max(prev - 0.2, 0.5))}

                          disabled={assetZoomLevel <= 0.5}

                        >

                          <ZoomOut className="h-4 w-4" />

                        </Button>

                        <span className="text-xs text-muted-foreground">{Math.round(assetZoomLevel * 100)}%</span>

                        <Button

                          variant="outline"

                          size="sm"

                          onClick={() => setAssetZoomLevel((prev) => Math.min(prev + 0.2, 3))}

                          disabled={assetZoomLevel >= 3}

                        >

                          <ZoomIn className="h-4 w-4" />

                        </Button>

                        <Button

                          variant="outline"

                          size="sm"

                          onClick={() => setAssetZoomLevel(1)}

                          disabled={assetZoomLevel === 1}

                        >

                          Reset

                        </Button>

                      </div>

                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">

                      <div>

                        <p className="text-muted-foreground">ID:</p>

                        <p>{selectedAsset.id}</p>

                      </div>

                      <div>

                        <p className="text-muted-foreground">Original ID:</p>

                        <p>{selectedAsset.originalId}</p>

                      </div>

                      <div>

                        <p className="text-muted-foreground">MIME Type:</p>

                        <p>{selectedAsset.mimeType}</p>

                      </div>

                      <div>

                        <p className="text-muted-foreground">Path:</p>

                        <p className="truncate">{selectedAsset.publicPath}</p>

                      </div>

                    </div>

                    <div className="mt-4 flex justify-end">

                      <Button

                        variant="outline"

                        size="sm"

                        onClick={() => {

                          const link = document.createElement("a")

                          link.href = selectedAsset.publicPath

                          link.download = `${selectedAsset.originalId}.${selectedAsset.mimeType.split("/")[1]}`

                          document.body.appendChild(link)

                          link.click()

                          document.body.removeChild(link)

                        }}

                      >

                        <Download className="h-4 w-4 mr-2" />

                        Download Asset

                      </Button>

                    </div>

                  </div>

                </div>

              )}

            </TabsContent>

          </Tabs>

        </div>

      </div>

    </div>

  )

}

================
File: components\sample-pdf-option.tsx
================
"use client"



import { FileIcon } from "lucide-react"

import { Button } from "@/components/ui/button"



interface SamplePdfOptionProps {

  onSelect: (file: File, isSample: boolean) => void

}



export function SamplePdfOption({ onSelect }: SamplePdfOptionProps) {

  // Create a sample PDF with PMI data and graph

  const createSamplePdf = async () => {

    try {

      // Fetch the PMI graph image

      const logoResponse = await fetch(

        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-1.jpeg-0afae6fb-OLkYscR0PxBzexzNs6sQJ6v8H4dKv2.jpeg",

      )

      const logoBlob = await logoResponse.blob()



      // Create a simple PDF with text content and the PMI graph

      // This is a simplified approach - in a real app, you'd use a PDF generation library

      const pdfContent = new Uint8Array([

        // This is a minimal PDF file with just text

        37, 80, 68, 70, 45, 49, 46, 52, 10, 37, 226, 227, 207, 211, 10, 49, 32, 48, 32, 111, 98, 106, 10, 60, 60, 47,

        84, 105, 116, 108, 101, 40, 80, 77, 73, 32, 68, 97, 116, 97, 32, 82, 101, 112, 111, 114, 116, 41, 47, 65, 117,

        116, 104, 111, 114, 40, 69, 99, 111, 110, 111, 109, 105, 99, 32, 82, 101, 115, 101, 97, 114, 99, 104, 41, 47,

        67, 114, 101, 97, 116, 111, 114, 40, 77, 105, 115, 116, 114, 97, 108, 32, 79, 67, 82, 32, 84, 101, 115, 116, 41,

        47, 80, 114, 111, 100, 117, 99, 101, 114, 40, 77, 105, 115, 116, 114, 97, 108, 32, 79, 67, 82, 32, 84, 101, 115,

        116, 41, 47, 67, 114, 101, 97, 116, 105, 111, 110, 68, 97, 116, 101, 40, 68, 58, 50, 48, 50, 51, 48, 51, 50, 50,

        49, 50, 48, 48, 48, 48, 41, 62, 62, 10, 101, 110, 100, 111, 98, 106, 10, 50, 32, 48, 32, 111, 98, 106, 10, 60,

        60, 47, 84, 121, 112, 101, 47, 67, 97, 116, 97, 108, 111, 103, 47, 80, 97, 103, 101, 115, 32, 51, 32, 48, 32,

        82, 62, 62, 10, 101, 110, 100, 111, 98, 106, 10, 51, 32, 48, 32, 111, 98, 106, 10, 60, 60, 47, 84, 121, 112,

        101, 47, 80, 97, 103, 101, 115, 47, 67, 111, 117, 110, 116, 32, 49, 47, 75, 105, 100, 115, 91, 52, 32, 48, 32,

        82, 93, 62, 62, 10, 101, 110, 100, 111, 98, 106, 10, 52, 32, 48, 32, 111, 98, 106, 10, 60, 60, 47, 84, 121, 112,

        101, 47, 80, 97, 103, 101, 47, 80, 97, 114, 101, 110, 116, 32, 51, 32, 48, 32, 82, 47, 82, 101, 115, 111, 117,

        114, 99, 101, 115, 60, 60, 47, 70, 111, 110, 116, 60, 60, 47, 70, 49, 32, 53, 32, 48, 32, 82, 62, 62, 62, 62,

        47, 77, 101, 100, 105, 97, 66, 111, 120, 91, 48, 32, 48, 32, 54, 49, 50, 32, 55, 57, 50, 93, 47, 67, 111, 110,

        116, 101, 110, 116, 115, 32, 54, 32, 48, 32, 82, 62, 62, 10, 101, 110, 100, 111, 98, 106, 10, 53, 32, 48, 32,

        111, 98, 106, 10, 60, 60, 47, 84, 121, 112, 101, 47, 70, 111, 110, 116, 47, 83, 117, 98, 116, 121, 112, 101, 47,

        84, 121, 112, 101, 49, 47, 66, 97, 115, 101, 70, 111, 110, 116, 47, 72, 101, 108, 118, 101, 116, 105, 99, 97,

        62, 62, 10, 101, 110, 100, 111, 98, 106, 10, 54, 32, 48, 32, 111, 98, 106, 10, 60, 60, 47, 76, 101, 110, 103,

        116, 104, 32, 52, 52, 62, 62, 10, 115, 116, 114, 101, 97, 109, 10, 66, 84, 10, 47, 70, 49, 32, 49, 56, 32, 84,

        102, 10, 49, 48, 48, 32, 55, 48, 48, 32, 84, 100, 10, 40, 80, 77, 73, 32, 73, 110, 112, 117, 116, 47, 79, 117,

        116, 112, 117, 116, 32, 80, 114, 105, 99, 101, 32, 65, 110, 97, 108, 121, 115, 105, 115, 41, 32, 84, 106, 10,

        69, 84, 10, 101, 110, 100, 115, 116, 114, 101, 97, 109, 10, 101, 110, 100, 111, 98, 106, 10, 120, 114, 101, 102,

        10, 48, 32, 55, 10, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 32, 54, 53, 53, 51, 53, 32, 102, 10, 48, 48, 48, 48,

        48, 48, 48, 48, 49, 48, 32, 48, 48, 48, 48, 48, 32, 110, 10, 48, 48, 48, 48, 48, 48, 48, 49, 56, 55, 32, 48, 48,

        48, 48, 48, 32, 110, 10, 48, 48, 48, 48, 48, 48, 48, 50, 51, 54, 32, 48, 48, 48, 48, 48, 32, 110, 10, 48, 48,

        48, 48, 48, 48, 48, 50, 57, 51, 32, 48, 48, 48, 48, 48, 32, 110, 10, 48, 48, 48, 48, 48, 48, 48, 52, 49, 56, 32,

        48, 48, 48, 48, 48, 32, 110, 10, 48, 48, 48, 48, 48, 48, 48, 52, 55, 56, 32, 48, 48, 48, 48, 48, 32, 110, 10,

        116, 114, 97, 105, 108, 101, 114, 10, 60, 60, 47, 83, 105, 122, 101, 32, 55, 47, 82, 111, 111, 116, 32, 50, 32,

        48, 32, 82, 47, 73, 110, 102, 111, 32, 49, 32, 48, 32, 82, 62, 62, 10, 115, 116, 97, 114, 116, 120, 114, 101,

        102, 10, 54, 50, 55, 10, 37, 37, 69, 79, 70,

      ])



      const blob = new Blob([pdfContent], { type: "application/pdf" })

      const file = new File([blob], "pmi-economic-report.pdf", { type: "application/pdf" })



      // Pass true as the second parameter to indicate this is a sample PDF

      onSelect(file, true)

    } catch (error) {

      console.error("Error creating sample PDF:", error)

    }

  }



  return (

    <div className="mt-6 border-t pt-4">

      <h3 className="text-sm font-medium mb-2">Or try with a sample PDF:</h3>

      <div className="flex flex-wrap gap-2">

        <Button variant="outline" size="sm" className="flex items-center gap-2" onClick={createSamplePdf}>

          <FileIcon className="h-4 w-4" />

          PMI Economic Report

        </Button>

      </div>

    </div>

  )

}

================
File: components\theme-provider.tsx
================
"use client"

import { ThemeProvider as NextThemesProvider } from "next-themes"

import type { ThemeProviderProps } from "next-themes"



export function ThemeProvider({ children, ...props }: ThemeProviderProps) {

  return <NextThemesProvider {...props}>{children}</NextThemesProvider>

}

================
File: components\theme-toggle.tsx
================
"use client"



import { useTheme } from "next-themes"

import { Button } from "@/components/ui/button"

import { Moon, Sun } from "lucide-react"

import { useEffect, useState } from "react"



export function ThemeToggle() {

  const { theme, setTheme } = useTheme()

  const [mounted, setMounted] = useState(false)



  // useEffect only runs on the client, so we can safely show the UI once mounted

  useEffect(() => {

    setMounted(true)

  }, [])



  if (!mounted) {

    return null

  }



  return (

    <Button

      variant="ghost"

      size="icon"

      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}

      aria-label={theme === "dark" ? "Switch to light mode" : "Switch to dark mode"}

    >

      {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}

    </Button>

  )

}

================
File: components\ui\alert.tsx
================
import * as React from "react"

import { cva, type VariantProps } from "class-variance-authority"



import { cn } from "@/lib/utils"



const alertVariants = cva(

  "relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",

  {

    variants: {

      variant: {

        default: "bg-card text-card-foreground",

        destructive:

          "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90",

      },

    },

    defaultVariants: {

      variant: "default",

    },

  }

)



function Alert({

  className,

  variant,

  ...props

}: React.ComponentProps<"div"> & VariantProps<typeof alertVariants>) {

  return (

    <div

      data-slot="alert"

      role="alert"

      className={cn(alertVariants({ variant }), className)}

      {...props}

    />

  )

}



function AlertTitle({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="alert-title"

      className={cn(

        "col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",

        className

      )}

      {...props}

    />

  )

}



function AlertDescription({

  className,

  ...props

}: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="alert-description"

      className={cn(

        "text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",

        className

      )}

      {...props}

    />

  )

}



export { Alert, AlertTitle, AlertDescription }

================
File: components\ui\button.tsx
================
import * as React from "react"

import { Slot } from "@radix-ui/react-slot"

import { cva, type VariantProps } from "class-variance-authority"



import { cn } from "@/lib/utils"



const buttonVariants = cva(

  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",

  {

    variants: {

      variant: {

        default: "bg-primary text-primary-foreground hover:bg-primary/90",

        destructive:

          "bg-destructive text-destructive-foreground hover:bg-destructive/90",

        outline:

          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",

        secondary:

          "bg-secondary text-secondary-foreground hover:bg-secondary/80",

        ghost: "hover:bg-accent hover:text-accent-foreground",

        link: "text-primary underline-offset-4 hover:underline",

      },

      size: {

        default: "h-10 px-4 py-2",

        sm: "h-9 rounded-md px-3",

        lg: "h-11 rounded-md px-8",

        icon: "h-10 w-10",

      },

    },

    defaultVariants: {

      variant: "default",

      size: "default",

    },

  }

)



export interface ButtonProps

  extends React.ButtonHTMLAttributes<HTMLButtonElement>,

    VariantProps<typeof buttonVariants> {

  asChild?: boolean

}



const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(

  ({ className, variant, size, asChild = false, ...props }, ref) => {

    const Comp = asChild ? Slot : "button"

    return (

      <Comp

        className={cn(buttonVariants({ variant, size, className }))}

        ref={ref}

        {...props}

      />

    )

  }

)

Button.displayName = "Button"



export { Button, buttonVariants }

================
File: components\ui\card.tsx
================
import * as React from "react"



import { cn } from "@/lib/utils"



function Card({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card"

      className={cn(

        "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",

        className

      )}

      {...props}

    />

  )

}



function CardHeader({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card-header"

      className={cn(

        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",

        className

      )}

      {...props}

    />

  )

}



function CardTitle({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card-title"

      className={cn("leading-none font-semibold", className)}

      {...props}

    />

  )

}



function CardDescription({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card-description"

      className={cn("text-muted-foreground text-sm", className)}

      {...props}

    />

  )

}



function CardAction({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card-action"

      className={cn(

        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",

        className

      )}

      {...props}

    />

  )

}



function CardContent({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card-content"

      className={cn("px-6", className)}

      {...props}

    />

  )

}



function CardFooter({ className, ...props }: React.ComponentProps<"div">) {

  return (

    <div

      data-slot="card-footer"

      className={cn("flex items-center px-6 [.border-t]:pt-6", className)}

      {...props}

    />

  )

}



export {

  Card,

  CardHeader,

  CardFooter,

  CardTitle,

  CardAction,

  CardDescription,

  CardContent,

}

================
File: components\ui\input.tsx
================
import * as React from "react"



import { cn } from "@/lib/utils"



function Input({ className, type, ...props }: React.ComponentProps<"input">) {

  return (

    <input

      type={type}

      data-slot="input"

      className={cn(

        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",

        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",

        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",

        className

      )}

      {...props}

    />

  )

}



export { Input }

================
File: components\ui\progress.tsx
================
"use client"



import * as React from "react"

import * as ProgressPrimitive from "@radix-ui/react-progress"



import { cn } from "@/lib/utils"



function Progress({

  className,

  value,

  ...props

}: React.ComponentProps<typeof ProgressPrimitive.Root>) {

  return (

    <ProgressPrimitive.Root

      data-slot="progress"

      className={cn(

        "bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",

        className

      )}

      {...props}

    >

      <ProgressPrimitive.Indicator

        data-slot="progress-indicator"

        className="bg-primary h-full w-full flex-1 transition-all"

        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}

      />

    </ProgressPrimitive.Root>

  )

}



export { Progress }

================
File: components\ui\tabs.tsx
================
"use client"



import * as React from "react"

import * as TabsPrimitive from "@radix-ui/react-tabs"



import { cn } from "@/lib/utils"



function Tabs({

  className,

  ...props

}: React.ComponentProps<typeof TabsPrimitive.Root>) {

  return (

    <TabsPrimitive.Root

      data-slot="tabs"

      className={cn("flex flex-col gap-2", className)}

      {...props}

    />

  )

}



function TabsList({

  className,

  ...props

}: React.ComponentProps<typeof TabsPrimitive.List>) {

  return (

    <TabsPrimitive.List

      data-slot="tabs-list"

      className={cn(

        "bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",

        className

      )}

      {...props}

    />

  )

}



function TabsTrigger({

  className,

  ...props

}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {

  return (

    <TabsPrimitive.Trigger

      data-slot="tabs-trigger"

      className={cn(

        "data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",

        className

      )}

      {...props}

    />

  )

}



function TabsContent({

  className,

  ...props

}: React.ComponentProps<typeof TabsPrimitive.Content>) {

  return (

    <TabsPrimitive.Content

      data-slot="tabs-content"

      className={cn("flex-1 outline-none", className)}

      {...props}

    />

  )

}



export { Tabs, TabsList, TabsTrigger, TabsContent }

================
File: components\ui\tooltip.tsx
================
"use client"



import * as React from "react"

import * as TooltipPrimitive from "@radix-ui/react-tooltip"



import { cn } from "@/lib/utils"



function TooltipProvider({

  delayDuration = 0,

  ...props

}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {

  return (

    <TooltipPrimitive.Provider

      data-slot="tooltip-provider"

      delayDuration={delayDuration}

      {...props}

    />

  )

}



function Tooltip({

  ...props

}: React.ComponentProps<typeof TooltipPrimitive.Root>) {

  return (

    <TooltipProvider>

      <TooltipPrimitive.Root data-slot="tooltip" {...props} />

    </TooltipProvider>

  )

}



function TooltipTrigger({

  ...props

}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {

  return <TooltipPrimitive.Trigger data-slot="tooltip-trigger" {...props} />

}



function TooltipContent({

  className,

  sideOffset = 0,

  children,

  ...props

}: React.ComponentProps<typeof TooltipPrimitive.Content>) {

  return (

    <TooltipPrimitive.Portal>

      <TooltipPrimitive.Content

        data-slot="tooltip-content"

        sideOffset={sideOffset}

        className={cn(

          "bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",

          className

        )}

        {...props}

      >

        {children}

        <TooltipPrimitive.Arrow className="bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]" />

      </TooltipPrimitive.Content>

    </TooltipPrimitive.Portal>

  )

}



export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }

================
File: hooks\use-window-size.ts
================
"use client"



import { useState, useEffect } from "react"



interface WindowSize {

  width: number | undefined

  height: number | undefined

}



export function useWindowSize(): WindowSize {

  const [windowSize, setWindowSize] = useState<WindowSize>({

    width: undefined,

    height: undefined,

  })



  useEffect(() => {

    // 윈도우 크기를 가져오는 핸들러 함수

    function handleResize() {

      setWindowSize({

        width: window.innerWidth,

        height: window.innerHeight,

      })

    }



    // 이벤트 리스너 추가

    window.addEventListener("resize", handleResize)



    // 초기 윈도우 크기 설정

    handleResize()



    // 클린업 함수

    return () => window.removeEventListener("resize", handleResize)

  }, [])



  return windowSize

}

================
File: lib\utils.ts
================
import { clsx, type ClassValue } from "clsx"

import { twMerge } from "tailwind-merge"



export function cn(...inputs: ClassValue[]) {

  return twMerge(clsx(inputs))

}

================
File: lib\server\asset-store.ts
================
import "server-only"

import fs from "fs"

import path from "path"

import { v4 as uuidv4 } from "uuid"

import { put } from "@vercel/blob"



// 이미지 저장 경로 설정

const ASSET_DIR = path.join(process.cwd(), "public", "assets", "ocr-images")



// 이미지 저장 타입 정의

interface SavedImageAsset {

  id: string

  originalId: string

  filePath?: string // 선택적 - Blob에서는 사용하지 않음

  publicPath: string

  mimeType: string

  width?: number

  height?: number

}



// 이미지 맵 타입 정의

type ImageMap = Record<string, string>



/**

 * 디렉토리가 존재하는지 확인하고 없으면 생성

 */

function ensureDirectoryExists(directory: string): void {

  if (!fs.existsSync(directory)) {

    fs.mkdirSync(directory, { recursive: true })

  }

}



/**

 * Base64 데이터에서 MIME 타입 추출

 */

function getMimeTypeFromBase64(base64Data: string): string {

  // Base64 데이터에서 MIME 타입 추출 시도

  const matches = base64Data.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/)

  return matches && matches.length > 1 ? matches[1] : "image/png" // 기본값은 PNG

}



/**

 * Base64 데이터에서 실제 이미지 데이터만 추출

 */

function extractBase64Data(base64Data: string): string {

  const matches = base64Data.match(/^data:image\/[a-zA-Z0-9-.+]+;base64,(.+)$/)

  return matches && matches.length > 1 ? matches[1] : base64Data

}



/**

 * 파일 시스템에 이미지 저장 (로컬 개발 환경용)

 */

async function storeImageToFileSystem(

  imageId: string,

  base64Data: string,

  sessionDir: string,

): Promise<SavedImageAsset> {

  try {

    // MIME 타입 추출

    const mimeType = getMimeTypeFromBase64(base64Data)



    // 파일 확장자 결정

    const extension = mimeType.split("/")[1] || "png"



    // 파일명 생성 (원본 ID + UUID)

    const fileName = `${imageId}-${uuidv4().slice(0, 8)}.${extension}`



    // 파일 경로 설정

    const filePath = path.join(sessionDir, fileName)



    // 공개 경로 설정 (클라이언트에서 접근 가능한 경로)

    const publicPath = `/assets/ocr-images/${path.basename(sessionDir)}/${fileName}`



    // Base64 데이터 추출

    const base64Content = extractBase64Data(base64Data)



    // 파일로 저장

    fs.writeFileSync(filePath, Buffer.from(base64Content, "base64"))



    // 저장된 이미지 정보 기록

    return {

      id: fileName.split(".")[0],

      originalId: imageId,

      filePath,

      publicPath,

      mimeType,

    }

  } catch (error) {

    console.error(`Failed to save image ${imageId} to file system:`, error)

    throw error

  }

}



/**

 * Vercel Blob Storage에 이미지 저장 (프로덕션 환경용)

 */

async function storeImageToBlob(imageId: string, base64Data: string, sessionId: string): Promise<SavedImageAsset> {

  try {

    // MIME 타입 추출

    const mimeType = getMimeTypeFromBase64(base64Data)



    // 파일 확장자 결정

    const extension = mimeType.split("/")[1] || "png"



    // 파일명 생성 (세션 ID 포함)

    const fileName = `ocr-images/${sessionId}/${imageId}-${uuidv4().slice(0, 8)}.${extension}`



    // Base64 데이터 추출

    const base64Content = extractBase64Data(base64Data)



    // Buffer를 Blob 객체로 변환 (브라우저 환경에서는 이렇게 하지만 Node.js에서는 다른 방법 필요)

    // 수정: Buffer 대신 base64 문자열을 직접 사용

    const buffer = Buffer.from(base64Content, "base64")



    // Buffer를 Uint8Array로 변환

    const uint8Array = new Uint8Array(buffer)



    // Uint8Array를 Blob으로 변환

    const blob = new Blob([uint8Array], { type: mimeType })



    // Vercel Blob Storage에 업로드

    const uploadedBlob = await put(fileName, blob, {

      contentType: mimeType,

      access: "public",

    })



    console.log(`Image saved to Blob Storage: ${uploadedBlob.url}`)



    // 저장된 이미지 정보 기록

    return {

      id: fileName.split("/").pop()?.split(".")[0] || imageId,

      originalId: imageId,

      publicPath: uploadedBlob.url,

      mimeType,

    }

  } catch (error) {

    console.error(`Failed to save image ${imageId} to Blob Storage:`, error)

    throw error

  }

}



/**

 * 이미지 맵을 사용하여 이미지를 저장

 * 환경에 따라 파일 시스템 또는 Blob Storage 사용

 */

export async function storeImagesFromMap(

  imageMap: ImageMap,

  sessionId?: string,

): Promise<Record<string, SavedImageAsset>> {

  // 세션 ID가 없으면 생성

  const session = sessionId || uuidv4()



  // 저장된 이미지 정보를 담을 객체

  const savedImages: Record<string, SavedImageAsset> = {}



  // Vercel 환경 여부 확인

  const isVercelEnvironment = process.env.VERCEL === "1"



  if (!isVercelEnvironment) {

    // 로컬 개발 환경: 파일 시스템 사용

    console.log(`Using file system storage for session ${session}`)



    // 세션별 디렉토리 경로

    const sessionDir = path.join(ASSET_DIR, session)



    // 디렉토리 존재 확인

    ensureDirectoryExists(sessionDir)



    // 이미지 맵의 각 항목을 처리

    for (const [imageId, base64Data] of Object.entries(imageMap)) {

      try {

        // 파일 시스템에 이미지 저장

        savedImages[imageId] = await storeImageToFileSystem(imageId, base64Data, sessionDir)

        console.log(`Image saved to file system: ${savedImages[imageId].publicPath}`)

      } catch (error) {

        console.error(`Failed to save image ${imageId}:`, error)

      }

    }



    // 세션 정보 저장 (나중에 정리를 위해)

    const sessionInfoPath = path.join(sessionDir, "session-info.json")

    fs.writeFileSync(

      sessionInfoPath,

      JSON.stringify({

        sessionId: session,

        createdAt: new Date().toISOString(),

        imageCount: Object.keys(savedImages).length,

      }),

    )

  } else {

    // Vercel 환경: Blob Storage 사용

    console.log(`Using Blob Storage for session ${session}`)



    // 이미지 맵의 각 항목을 처리

    for (const [imageId, base64Data] of Object.entries(imageMap)) {

      try {

        // Blob Storage에 이미지 저장

        savedImages[imageId] = await storeImageToBlob(imageId, base64Data, session)

      } catch (error) {

        console.error(`Failed to save image ${imageId}:`, error)

      }

    }

  }



  return savedImages

}



/**

 * 세션 ID로 저장된 이미지 목록 가져오기

 * 로컬 개발 환경에서만 작동 (Vercel 환경에서는 빈 배열 반환)

 */

export function getStoredImagesBySession(sessionId: string): SavedImageAsset[] {

  // Vercel 환경에서는 작동하지 않음

  if (process.env.VERCEL === "1") {

    console.log("getStoredImagesBySession not supported in Vercel environment")

    return []

  }



  const sessionDir = path.join(ASSET_DIR, sessionId)



  if (!fs.existsSync(sessionDir)) {

    return []

  }



  // 세션 정보 파일 경로

  const sessionInfoPath = path.join(sessionDir, "session-info.json")



  // 세션 정보 파일이 없으면 빈 배열 반환

  if (!fs.existsSync(sessionInfoPath)) {

    return []

  }



  // 디렉토리 내 모든 파일 목록 가져오기

  const files = fs.readdirSync(sessionDir).filter((file) => file !== "session-info.json" && !file.startsWith("."))



  // 각 파일에 대한 정보 수집

  return files.map((fileName) => {

    const filePath = path.join(sessionDir, fileName)

    const publicPath = `/assets/ocr-images/${sessionId}/${fileName}`

    const fileNameParts = fileName.split(".")

    const extension = fileNameParts.pop() || "png"

    const id = fileNameParts.join(".")

    const originalIdParts = id.split("-")

    const originalId = originalIdParts.slice(0, -1).join("-") || id



    return {

      id,

      originalId,

      filePath,

      publicPath,

      mimeType: `image/${extension}`,

    }

  })

}



/**

 * 세션 ID로 저장된 이미지 삭제

 * 로컬 개발 환경에서만 작동 (Vercel 환경에서는 false 반환)

 */

export function deleteStoredImagesBySession(sessionId: string): boolean {

  // Vercel 환경에서는 작동하지 않음

  if (process.env.VERCEL === "1") {

    console.log("deleteStoredImagesBySession not supported in Vercel environment")

    return false

  }



  const sessionDir = path.join(ASSET_DIR, sessionId)



  if (!fs.existsSync(sessionDir)) {

    return false

  }



  try {

    // 디렉토리 내 모든 파일 삭제

    const files = fs.readdirSync(sessionDir)

    for (const file of files) {

      fs.unlinkSync(path.join(sessionDir, file))

    }



    // 디렉토리 삭제

    fs.rmdirSync(sessionDir)



    return true

  } catch (error) {

    console.error(`Failed to delete session ${sessionId}:`, error)

    return false

  }

}

================
File: public\file.svg
================
<svg fill="none" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 13.5V5.41a1 1 0 0 0-.3-.7L9.8.29A1 1 0 0 0 9.08 0H1.5v13.5A2.5 2.5 0 0 0 4 16h8a2.5 2.5 0 0 0 2.5-2.5m-1.5 0v-7H8v-5H3v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1M9.5 5V2.12L12.38 5zM5.13 5h-.62v1.25h2.12V5zm-.62 3h7.12v1.25H4.5zm.62 3h-.62v1.25h7.12V11z" clip-rule="evenodd" fill="#666" fill-rule="evenodd"/></svg>

================
File: public\globe.svg
================
<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><g clip-path="url(#a)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.27 14.1a6.5 6.5 0 0 0 3.67-3.45q-1.24.21-2.7.34-.31 1.83-.97 3.1M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.48-1.52a7 7 0 0 1-.96 0H7.5a4 4 0 0 1-.84-1.32q-.38-.89-.63-2.08a40 40 0 0 0 3.92 0q-.25 1.2-.63 2.08a4 4 0 0 1-.84 1.31zm2.94-4.76q1.66-.15 2.95-.43a7 7 0 0 0 0-2.58q-1.3-.27-2.95-.43a18 18 0 0 1 0 3.44m-1.27-3.54a17 17 0 0 1 0 3.64 39 39 0 0 1-4.3 0 17 17 0 0 1 0-3.64 39 39 0 0 1 4.3 0m1.1-1.17q1.45.13 2.69.34a6.5 6.5 0 0 0-3.67-3.44q.65 1.26.98 3.1M8.48 1.5l.01.02q.41.37.84 **********.63 2.08a40 40 0 0 0-3.92 0q.25-1.2.63-2.08a4 4 0 0 1 .85-1.32 7 7 0 0 1 .96 0m-2.75.4a6.5 6.5 0 0 0-3.67 3.44 29 29 0 0 1 2.7-.34q.31-1.83.97-3.1M4.58 6.28q-1.66.16-2.95.43a7 7 0 0 0 0 2.58q1.3.27 2.95.43a18 18 0 0 1 0-3.44m.17 4.71q-1.45-.12-2.69-.34a6.5 6.5 0 0 0 3.67 3.44q-.65-1.27-.98-3.1" fill="#666"/></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h16v16H0z"/></clipPath></defs></svg>

================
File: public\next.svg
================
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 394 80"><path fill="#000" d="M262 0h68.5v12.7h-27.2v66.6h-13.6V12.7H262V0ZM149 0v12.7H94v20.4h44.3v12.6H94v21h55v12.6H80.5V0h68.7zm34.3 0h-17.8l63.8 79.4h17.9l-32-39.7 32-39.6h-17.9l-23 28.6-23-28.6zm18.3 56.7-9-11-27.1 33.7h17.8l18.3-22.7z"/><path fill="#000" d="M81 79.3 17 0H0v79.3h13.6V17l50.2 62.3H81Zm252.6-.4c-1 0-1.8-.4-2.5-1s-1.1-1.6-1.1-2.6.3-1.8 1-2.5 1.6-1 2.6-1 1.8.3 2.5 1a3.4 3.4 0 0 1 .6 4.3 3.7 3.7 0 0 1-3 1.8zm23.2-33.5h6v23.3c0 2.1-.4 4-1.3 5.5a9.1 9.1 0 0 1-3.8 3.5c-1.6.8-3.5 1.3-5.7 1.3-2 0-3.7-.4-5.3-1s-2.8-1.8-3.7-3.2c-.9-1.3-1.4-3-1.4-5h6c.1.8.3 1.6.7 2.2s1 1.2 1.6 1.5c.7.4 1.5.5 2.4.5 1 0 1.8-.2 2.4-.6a4 4 0 0 0 1.6-1.8c.3-.8.5-1.8.5-3V45.5zm30.9 9.1a4.4 4.4 0 0 0-2-3.3 7.5 7.5 0 0 0-4.3-1.1c-1.3 0-2.4.2-3.3.5-.9.4-1.6 1-2 1.6a3.5 3.5 0 0 0-.3 4c.******* 1.3 1.2l1.8 1 2 .5 3.2.8c1.3.3 2.5.7 3.7 1.2a13 13 0 0 1 3.2 1.8 8.1 8.1 0 0 1 3 6.5c0 2-.5 3.7-1.5 5.1a10 10 0 0 1-4.4 3.5c-1.8.8-4.1 1.2-6.8 1.2-2.6 0-4.9-.4-6.8-1.2-2-.8-3.4-2-4.5-3.5a10 10 0 0 1-1.7-5.6h6a5 5 0 0 0 3.5 4.6c1 .4 2.2.6 3.4.6 1.3 0 2.5-.2 3.5-.6 1-.4 1.8-1 2.4-1.7a4 4 0 0 0 .8-2.4c0-.9-.2-1.6-.7-2.2a11 11 0 0 0-2.1-1.4l-3.2-1-3.8-1c-2.8-.7-5-1.7-6.6-3.2a7.2 7.2 0 0 1-2.4-5.7 8 8 0 0 1 1.7-5 10 10 0 0 1 4.3-3.5c2-.8 4-1.2 6.4-1.2 2.3 0 4.4.4 6.2 1.2 1.8.8 3.2 2 4.3 3.4 1 1.4 1.5 3 1.5 5h-5.8z"/></svg>

================
File: public\vercel.svg
================
<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1155 1000"><path d="m577.3 0 577.4 1000H0z" fill="#fff"/></svg>

================
File: public\window.svg
================
<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.5 2.5h13v10a1 1 0 0 1-1 1h-11a1 1 0 0 1-1-1zM0 1h16v11.5a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 0 12.5zm3.75 4.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5M7 4.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0m1.75.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5" fill="#666"/></svg>
