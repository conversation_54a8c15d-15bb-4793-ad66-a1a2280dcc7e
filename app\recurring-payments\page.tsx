'use client'

import { AppLayout } from '@/components/layouts/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Repeat, Plus, Calendar, DollarSign } from 'lucide-react'

export default function RecurringPaymentsPage() {
  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Recurring Payments</h1>
            <p className="text-gray-600">
              Manage your subscriptions, bills, and recurring expenses
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Recurring Payment
          </Button>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Monthly Commitments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">$0.00</div>
              <p className="text-xs text-gray-500">Total monthly recurring</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">0</div>
              <p className="text-xs text-gray-500">Currently active</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Next Payment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">--</div>
              <p className="text-xs text-gray-500">Days until next</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Active Recurring Payments</CardTitle>
              <CardDescription>
                Your current subscriptions and recurring bills
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Repeat className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No recurring payments yet
                </h3>
                <p className="text-gray-600 mb-4">
                  Add your subscriptions and bills to track monthly commitments
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add First Payment
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Upcoming Payments</CardTitle>
              <CardDescription>
                Payments due in the next 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No upcoming payments
                </h3>
                <p className="text-gray-600">
                  Set up recurring payments to see upcoming due dates
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Setup</CardTitle>
            <CardDescription>
              Common recurring payments you might want to add
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { name: 'Rent/Mortgage', icon: '🏠' },
                { name: 'Utilities', icon: '⚡' },
                { name: 'Internet', icon: '🌐' },
                { name: 'Phone', icon: '📱' },
                { name: 'Insurance', icon: '🛡️' },
                { name: 'Streaming', icon: '📺' },
                { name: 'Gym', icon: '💪' },
                { name: 'Other', icon: '➕' },
              ].map((item) => (
                <Button
                  key={item.name}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <span className="text-2xl">{item.icon}</span>
                  <span className="text-sm">{item.name}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
