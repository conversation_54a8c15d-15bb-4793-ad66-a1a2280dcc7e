-- Default Categories for Personal Finance Tracker
-- These will be inserted when a new user signs up

-- Function to create default categories for a new user
CREATE OR REPLACE FUNCTION create_default_categories(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.categories (user_id, name, color, icon, is_default) VALUES
    -- Income Categories
    (user_uuid, 'Salary', '#10B981', 'Banknote', true),
    (user_uuid, 'Freelance', '#3B82F6', 'Briefcase', true),
    (user_uuid, 'Investment', '#8B5CF6', 'TrendingUp', true),
    (user_uuid, 'Other Income', '#06B6D4', 'Plus', true),
    
    -- Expense Categories
    (user_uuid, 'Food & Dining', '#EF4444', 'Utensils', true),
    (user_uuid, 'Transportation', '#F59E0B', 'Car', true),
    (user_uuid, 'Shopping', '#EC4899', 'ShoppingBag', true),
    (user_uuid, 'Entertainment', '#8B5CF6', 'Film', true),
    (user_uuid, 'Bills & Utilities', '#6B7280', 'Receipt', true),
    (user_uuid, 'Healthcare', '#EF4444', 'Heart', true),
    (user_uuid, 'Education', '#3B82F6', 'GraduationCap', true),
    (user_uuid, 'Travel', '#10B981', 'Plane', true),
    (user_uuid, 'Home & Garden', '#F59E0B', 'Home', true),
    (user_uuid, 'Personal Care', '#EC4899', 'Sparkles', true),
    (user_uuid, 'Insurance', '#6B7280', 'Shield', true),
    (user_uuid, 'Taxes', '#DC2626', 'Calculator', true),
    (user_uuid, 'Savings', '#10B981', 'PiggyBank', true),
    (user_uuid, 'Miscellaneous', '#9CA3AF', 'MoreHorizontal', true);
END;
$$ LANGUAGE plpgsql;

-- Function to create default user profile and settings
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create user profile
    INSERT INTO public.user_profiles (id, full_name, currency_code)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name', 'USD');
    
    -- Create default categories
    PERFORM create_default_categories(NEW.id);
    
    -- Create default user settings
    INSERT INTO public.user_settings (user_id, dashboard_widgets, ai_preferences, notification_settings, theme_preferences)
    VALUES (
        NEW.id,
        '{"income_card": true, "expense_card": true, "balance_card": true, "cash_flow_chart": true, "spending_heatmap": true, "category_pie_chart": true, "goal_progress": true, "upcoming_payments": true, "recent_transactions": true}',
        '{"tone": "professional", "detail_level": "medium", "response_frequency": "normal"}',
        '{"payment_reminders": true, "goal_alerts": true, "budget_warnings": true}',
        '{"theme": "system", "color_scheme": "default"}'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile and defaults for new users
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
