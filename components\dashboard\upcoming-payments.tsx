'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { getUserRecurringPayments } from '@/lib/supabase/queries'
import { RecurringPayment } from '@/lib/types/database'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Plus, Clock, AlertCircle } from 'lucide-react'

interface UpcomingPayment extends RecurringPayment {
  daysUntilDue: number
  isOverdue: boolean
}

export function UpcomingPayments() {
  const { user, profile } = useAuth()
  const [upcomingPayments, setUpcomingPayments] = useState<UpcomingPayment[]>([])
  const [loading, setLoading] = useState(true)

  const currency = profile?.currency_code || 'USD'

  useEffect(() => {
    if (user) {
      loadUpcomingPayments()
    }
  }, [user])

  const loadUpcomingPayments = async () => {
    if (!user) return

    setLoading(true)
    try {
      const recurringPayments = await getUserRecurringPayments(user.id)
      const paymentsWithDays = processUpcomingPayments(recurringPayments)
      setUpcomingPayments(paymentsWithDays)
    } catch (error) {
      console.error('Error loading upcoming payments:', error)
      // Generate sample data for demonstration
      setUpcomingPayments(generateSampleUpcomingPayments())
    } finally {
      setLoading(false)
    }
  }

  const processUpcomingPayments = (payments: RecurringPayment[]): UpcomingPayment[] => {
    const today = new Date()
    
    return payments
      .filter(payment => payment.is_active)
      .map(payment => {
        const nextPaymentDate = new Date(payment.next_payment_date)
        const diffTime = nextPaymentDate.getTime() - today.getTime()
        const daysUntilDue = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        
        return {
          ...payment,
          daysUntilDue,
          isOverdue: daysUntilDue < 0,
        }
      })
      .filter(payment => payment.daysUntilDue <= 30) // Show payments due in next 30 days
      .sort((a, b) => a.daysUntilDue - b.daysUntilDue)
      .slice(0, 5) // Limit to 5 upcoming payments
  }

  const generateSampleUpcomingPayments = (): UpcomingPayment[] => {
    const today = new Date()
    
    const samplePayments = [
      {
        id: '1',
        user_id: user?.id || '',
        category_id: '1',
        name: 'Netflix Subscription',
        amount: 15.99,
        currency_code: currency,
        frequency: 'monthly' as const,
        start_date: '2024-01-01',
        end_date: null,
        next_payment_date: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        is_active: true,
        description: 'Streaming service subscription',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        daysUntilDue: 2,
        isOverdue: false,
        category: {
          id: '1',
          user_id: user?.id || '',
          name: 'Entertainment',
          color: '#8B5CF6',
          icon: 'Film',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
      {
        id: '2',
        user_id: user?.id || '',
        category_id: '2',
        name: 'Electric Bill',
        amount: 120.50,
        currency_code: currency,
        frequency: 'monthly' as const,
        start_date: '2024-01-01',
        end_date: null,
        next_payment_date: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        is_active: true,
        description: 'Monthly electricity bill',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        daysUntilDue: 5,
        isOverdue: false,
        category: {
          id: '2',
          user_id: user?.id || '',
          name: 'Bills & Utilities',
          color: '#6B7280',
          icon: 'Receipt',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
      {
        id: '3',
        user_id: user?.id || '',
        category_id: '3',
        name: 'Gym Membership',
        amount: 45.00,
        currency_code: currency,
        frequency: 'monthly' as const,
        start_date: '2024-01-01',
        end_date: null,
        next_payment_date: new Date(today.getTime() + 12 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        is_active: true,
        description: 'Monthly gym membership',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        daysUntilDue: 12,
        isOverdue: false,
        category: {
          id: '3',
          user_id: user?.id || '',
          name: 'Health & Fitness',
          color: '#10B981',
          icon: 'Heart',
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      },
    ]

    return samplePayments
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatDaysUntilDue = (days: number) => {
    if (days < 0) {
      return `${Math.abs(days)} days overdue`
    } else if (days === 0) {
      return 'Due today'
    } else if (days === 1) {
      return 'Due tomorrow'
    } else {
      return `Due in ${days} days`
    }
  }

  const getDueBadgeVariant = (days: number) => {
    if (days < 0) return 'destructive'
    if (days <= 3) return 'default'
    return 'secondary'
  }

  if (loading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Upcoming Payments</CardTitle>
          <CardDescription>Loading your scheduled payments...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (upcomingPayments.length === 0) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Upcoming Payments</CardTitle>
          <CardDescription>Your scheduled bills and subscriptions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No upcoming payments
            </h3>
            <p className="text-gray-600 mb-4">
              Set up recurring payments to track your bills and subscriptions
            </p>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Recurring Payment
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Upcoming Payments</CardTitle>
            <CardDescription>Bills and subscriptions due soon</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Payment
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingPayments.map((payment) => (
            <div key={payment.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div className={`p-2 rounded-full ${
                payment.isOverdue ? 'bg-red-100' : payment.daysUntilDue <= 3 ? 'bg-orange-100' : 'bg-blue-100'
              }`}>
                {payment.isOverdue ? (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                ) : payment.daysUntilDue <= 3 ? (
                  <Clock className="h-4 w-4 text-orange-600" />
                ) : (
                  <Calendar className="h-4 w-4 text-blue-600" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {payment.name}
                  </p>
                  {payment.category && (
                    <Badge variant="secondary" className="text-xs">
                      {payment.category.name}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <span className="capitalize">{payment.frequency}</span>
                  <span>•</span>
                  <Badge variant={getDueBadgeVariant(payment.daysUntilDue)} className="text-xs">
                    {formatDaysUntilDue(payment.daysUntilDue)}
                  </Badge>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-sm font-semibold text-gray-900">
                  {formatCurrency(payment.amount)}
                </p>
              </div>
            </div>
          ))}

          <div className="pt-4 border-t">
            <Button variant="ghost" className="w-full">
              View All Payments
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
